#!/usr/bin/env node

/**
 * Check API endpoint errors again - Comprehensive debugging
 */

const { chromium } = require('playwright');

async function checkApiErrorsAgain() {
    console.log('🔍 Checking API Endpoint Errors Again');
    console.log('URL: http://51club.local/#/');
    console.log('='.repeat(60));
    
    const browser = await chromium.launch({ 
        headless: false,
        devtools: true
    });
    
    const context = await browser.newContext({
        ignoreHTTPSErrors: true
    });
    
    const page = await context.newPage();
    
    // Comprehensive error tracking
    const allErrors = [];
    const apiErrors = [];
    const networkFailures = [];
    const consoleErrors = [];
    
    // Console monitoring
    page.on('console', msg => {
        const message = {
            type: msg.type(),
            text: msg.text(),
            location: msg.location(),
            timestamp: new Date().toISOString()
        };
        
        if (msg.type() === 'error') {
            consoleErrors.push(message);
            console.log(`❌ [CONSOLE ERROR] ${msg.text()}`);
            
            // Check for API-related errors
            if (msg.text().includes('api') || msg.text().includes('API') || 
                msg.text().includes('XMLHttpRequest') || msg.text().includes('fetch')) {
                apiErrors.push({
                    type: 'console',
                    message: msg.text(),
                    location: msg.location()
                });
            }
        } else if (msg.type() === 'warning') {
            console.log(`⚠️ [WARNING] ${msg.text()}`);
        }
    });
    
    // Network request monitoring
    page.on('request', request => {
        if (request.url().includes('api')) {
            console.log(`🌐 [REQUEST] ${request.method()} ${request.url()}`);
        }
    });
    
    // Network response monitoring
    page.on('response', response => {
        if (response.url().includes('api')) {
            const status = response.status();
            const icon = status >= 200 && status < 300 ? '✅' : 
                        status >= 400 ? '❌' : '⚠️';
            console.log(`${icon} [RESPONSE] ${status} ${response.url()}`);
            
            if (status >= 400) {
                apiErrors.push({
                    type: 'http',
                    url: response.url(),
                    status: status,
                    statusText: response.statusText()
                });
            }
        }
    });
    
    // Network failures
    page.on('requestfailed', request => {
        if (request.url().includes('api')) {
            console.log(`🔴 [NETWORK FAIL] ${request.url()} - ${request.failure()?.errorText}`);
            networkFailures.push({
                url: request.url(),
                method: request.method(),
                error: request.failure()?.errorText
            });
            
            apiErrors.push({
                type: 'network',
                url: request.url(),
                error: request.failure()?.errorText
            });
        }
    });
    
    try {
        console.log('🌐 Loading page and monitoring for 30 seconds...');
        
        const response = await page.goto('http://51club.local/#/', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Page Status: ${response.status()}`);
        
        // Wait and monitor for errors
        console.log('⏳ Monitoring for API errors...');
        await page.waitForTimeout(15000);
        
        // Try to trigger more API calls by interacting with the page
        console.log('🖱️ Attempting to trigger additional API calls...');
        
        try {
            // Try clicking buttons
            const buttons = await page.$$('button, .btn, [role="button"]');
            if (buttons.length > 0) {
                console.log(`Found ${buttons.length} clickable elements`);
                for (let i = 0; i < Math.min(3, buttons.length); i++) {
                    try {
                        await buttons[i].click();
                        await page.waitForTimeout(2000);
                    } catch (e) {
                        console.log(`Could not click button ${i + 1}`);
                    }
                }
            }
            
            // Try navigation if it's a SPA
            const links = await page.$$('a[href*="#"], .nav-link, .menu-item');
            if (links.length > 0) {
                console.log(`Found ${links.length} navigation links`);
                for (let i = 0; i < Math.min(2, links.length); i++) {
                    try {
                        await links[i].click();
                        await page.waitForTimeout(3000);
                    } catch (e) {
                        console.log(`Could not click link ${i + 1}`);
                    }
                }
            }
        } catch (e) {
            console.log('Could not interact with page elements');
        }
        
        // Final monitoring period
        console.log('⏳ Final monitoring period...');
        await page.waitForTimeout(10000);
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/api-errors-check.png',
            fullPage: true 
        });
        console.log('📸 Screenshot saved: api-errors-check.png');
        
        // Detailed analysis
        console.log('\n' + '='.repeat(60));
        console.log('📊 COMPREHENSIVE ERROR ANALYSIS');
        console.log('='.repeat(60));
        
        console.log(`📝 Total console errors: ${consoleErrors.length}`);
        console.log(`🌐 Network failures: ${networkFailures.length}`);
        console.log(`🔌 API-related errors: ${apiErrors.length}`);
        
        if (apiErrors.length > 0) {
            console.log('\n🔌 CURRENT API ERRORS:');
            console.log('─'.repeat(40));
            
            apiErrors.forEach((error, index) => {
                console.log(`\n${index + 1}. ${error.type?.toUpperCase()} ERROR:`);
                if (error.url) console.log(`   URL: ${error.url}`);
                if (error.status) console.log(`   Status: ${error.status} ${error.statusText}`);
                if (error.message) console.log(`   Message: ${error.message}`);
                if (error.error) console.log(`   Network Error: ${error.error}`);
                if (error.location) console.log(`   Location: ${error.location.url}:${error.location.lineNumber}`);
            });
            
            // Categorize errors
            const corsErrors = apiErrors.filter(e => e.message?.includes('CORS') || e.message?.includes('blocked'));
            const networkErrors = apiErrors.filter(e => e.type === 'network');
            const httpErrors = apiErrors.filter(e => e.type === 'http');
            
            console.log('\n📊 ERROR BREAKDOWN:');
            console.log(`   CORS Errors: ${corsErrors.length}`);
            console.log(`   Network Errors: ${networkErrors.length}`);
            console.log(`   HTTP Errors: ${httpErrors.length}`);
            
            // Specific fixes for current errors
            console.log('\n🔧 SPECIFIC FIXES NEEDED:');
            console.log('─'.repeat(30));
            
            if (corsErrors.length > 0) {
                console.log('🚫 CORS Issues:');
                corsErrors.forEach((error, i) => {
                    console.log(`   ${i + 1}. ${error.message}`);
                    if (error.message?.includes('multiple values')) {
                        console.log('      → Fix: Remove duplicate CORS headers');
                    } else if (error.message?.includes('not allowed')) {
                        console.log('      → Fix: Add missing headers to CORS policy');
                    }
                });
            }
            
            if (networkErrors.length > 0) {
                console.log('🌐 Network Issues:');
                networkErrors.forEach((error, i) => {
                    console.log(`   ${i + 1}. ${error.url} - ${error.error}`);
                    if (error.error?.includes('ERR_FAILED')) {
                        console.log('      → Fix: Check API server status and connectivity');
                    }
                });
            }
            
            if (httpErrors.length > 0) {
                console.log('🔴 HTTP Issues:');
                httpErrors.forEach((error, i) => {
                    console.log(`   ${i + 1}. ${error.status} ${error.statusText} - ${error.url}`);
                    if (error.status === 404) {
                        console.log('      → Fix: Check API endpoint exists');
                    } else if (error.status === 500) {
                        console.log('      → Fix: Check API server logs');
                    }
                });
            }
            
        } else {
            console.log('\n✅ NO API ERRORS DETECTED');
            console.log('   All API endpoints appear to be working correctly');
        }
        
        // Current status
        console.log('\n🎯 CURRENT STATUS:');
        if (apiErrors.length === 0) {
            console.log('🎉 SUCCESS: No API endpoint errors found!');
            console.log('✅ All API communications working properly');
        } else {
            console.log(`⚠️ ISSUES FOUND: ${apiErrors.length} API-related errors detected`);
            console.log('❌ Frontend-API communication has problems');
        }
        
        console.log('\n📋 NEXT STEPS:');
        if (apiErrors.length > 0) {
            console.log('1. Apply the specific fixes mentioned above');
            console.log('2. Test individual API endpoints');
            console.log('3. Check nginx and API server configurations');
            console.log('4. Verify CORS settings are correct');
        } else {
            console.log('✅ No action needed - API endpoints working correctly');
        }
        
    } catch (error) {
        console.error('❌ Error during analysis:', error.message);
    } finally {
        console.log('\n🔍 Keeping browser open for manual inspection...');
        console.log('Press Ctrl+C when done reviewing.');
        
        // Keep browser open for manual inspection
        await new Promise(() => {});
    }
}

checkApiErrorsAgain().catch(console.error);
