@echo off
echo 🔐 Installing SSL Certificates for 51Club Casino...
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Administrative privileges confirmed.
) else (
    echo ❌ ERROR: This script requires administrative privileges.
    echo Please run as Administrator.
    pause
    exit /b 1
)

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Docker is running.
) else (
    echo ❌ ERROR: Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo.
echo 📋 Extracting CA certificate from Docker container...

REM Create temp directory for certificates
if not exist "%TEMP%\51club-ssl" mkdir "%TEMP%\51club-ssl"

REM Extract CA certificate from nginx container
docker cp 51club_nginx:/etc/nginx/ssl/ca.crt "%TEMP%\51club-ssl\ca.crt" 2>nul

if exist "%TEMP%\51club-ssl\ca.crt" (
    echo ✅ CA certificate extracted successfully.
    echo.
    echo 🔧 Installing CA certificate to Windows Certificate Store...
    
    REM Import CA certificate to Trusted Root Certification Authorities
    certlm -addstore -f "Root" "%TEMP%\51club-ssl\ca.crt"
    
    if %errorLevel% == 0 (
        echo ✅ CA certificate installed successfully!
        echo.
        echo 🎉 SSL certificates are now trusted by Windows!
        echo.
        echo 🌐 You can now access your services without SSL warnings:
        echo   - Main Site: https://51club.local
        echo   - API: https://api.51club.local
        echo   - Games API: https://games.51club.local
        echo   - Admin Panel: https://admin.51club.local
        echo   - phpMyAdmin: https://db.51club.local
        echo.
        echo 💡 You may need to restart your browser for changes to take effect.
    ) else (
        echo ❌ Failed to install CA certificate.
        echo 💡 You can manually install the certificate:
        echo    1. Open "%TEMP%\51club-ssl\ca.crt"
        echo    2. Click "Install Certificate"
        echo    3. Choose "Local Machine"
        echo    4. Place in "Trusted Root Certification Authorities"
    )
    
    REM Clean up
    del "%TEMP%\51club-ssl\ca.crt"
    rmdir "%TEMP%\51club-ssl"
    
) else (
    echo ❌ Failed to extract CA certificate.
    echo 💡 Make sure the nginx container is running: docker-compose up -d
)

echo.
pause
