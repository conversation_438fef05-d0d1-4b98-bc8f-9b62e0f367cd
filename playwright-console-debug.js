#!/usr/bin/env node

/**
 * Playwright MCP Tool - Debug Console Errors at http://51club.local/#/
 */

const { chromium } = require('playwright');

async function debugConsoleErrors() {
    console.log('🎯 Playwright MCP Tool - Console Error Debug');
    console.log('URL: http://51club.local/#/');
    console.log('='.repeat(60));
    
    const browser = await chromium.launch({ 
        headless: false,
        devtools: true // Open DevTools automatically
    });
    
    const context = await browser.newContext({
        ignoreHTTPSErrors: true
    });
    
    const page = await context.newPage();
    
    // Collect console messages
    const consoleMessages = [];
    const networkErrors = [];
    const apiErrors = [];
    
    // Listen to console events
    page.on('console', msg => {
        const message = {
            type: msg.type(),
            text: msg.text(),
            location: msg.location(),
            timestamp: new Date().toISOString()
        };
        
        consoleMessages.push(message);
        
        // Log console messages in real-time
        const icon = msg.type() === 'error' ? '❌' : 
                   msg.type() === 'warning' ? '⚠️' : 
                   msg.type() === 'info' ? 'ℹ️' : '📝';
        
        console.log(`${icon} [${msg.type().toUpperCase()}] ${msg.text()}`);
        
        // Check for API-related errors
        if (msg.text().includes('api') || msg.text().includes('API') || 
            msg.text().includes('fetch') || msg.text().includes('XMLHttpRequest')) {
            apiErrors.push(message);
        }
    });
    
    // Listen to network failures
    page.on('requestfailed', request => {
        const error = {
            url: request.url(),
            method: request.method(),
            failure: request.failure(),
            timestamp: new Date().toISOString()
        };
        
        networkErrors.push(error);
        console.log(`🌐 [NETWORK ERROR] ${request.method()} ${request.url()} - ${request.failure()?.errorText}`);
        
        // Check for API endpoint failures
        if (request.url().includes('api') || request.url().includes('webapi')) {
            apiErrors.push({
                type: 'network',
                url: request.url(),
                method: request.method(),
                error: request.failure()?.errorText
            });
        }
    });
    
    // Listen to responses
    page.on('response', response => {
        if (response.status() >= 400) {
            const error = {
                url: response.url(),
                status: response.status(),
                statusText: response.statusText(),
                timestamp: new Date().toISOString()
            };
            
            console.log(`🔴 [HTTP ERROR] ${response.status()} ${response.statusText()} - ${response.url()}`);
            
            // Check for API endpoint errors
            if (response.url().includes('api') || response.url().includes('webapi')) {
                apiErrors.push({
                    type: 'http',
                    url: response.url(),
                    status: response.status(),
                    statusText: response.statusText()
                });
            }
        }
    });
    
    try {
        console.log('🌐 Navigating to http://51club.local/#/...');
        
        const response = await page.goto('http://51club.local/#/', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Page loaded: ${response.status()} ${response.statusText()}`);
        
        // Wait for page to fully load and execute JavaScript
        console.log('⏳ Waiting for page to fully load and execute JavaScript...');
        await page.waitForTimeout(5000);
        
        // Check if it's a Single Page Application (SPA)
        const pageInfo = await page.evaluate(() => {
            return {
                title: document.title,
                url: window.location.href,
                hasReact: typeof window.React !== 'undefined',
                hasVue: typeof window.Vue !== 'undefined',
                hasAngular: typeof window.angular !== 'undefined',
                hasJQuery: typeof window.$ !== 'undefined',
                scriptsCount: document.querySelectorAll('script').length,
                linksCount: document.querySelectorAll('link').length,
                bodyContent: document.body.innerHTML.length
            };
        });
        
        console.log('\n📊 Page Analysis:');
        console.log(`   Title: "${pageInfo.title}"`);
        console.log(`   URL: ${pageInfo.url}`);
        console.log(`   Has React: ${pageInfo.hasReact}`);
        console.log(`   Has Vue: ${pageInfo.hasVue}`);
        console.log(`   Has Angular: ${pageInfo.hasAngular}`);
        console.log(`   Has jQuery: ${pageInfo.hasJQuery}`);
        console.log(`   Scripts: ${pageInfo.scriptsCount}`);
        console.log(`   Links: ${pageInfo.linksCount}`);
        console.log(`   Body content: ${pageInfo.bodyContent} chars`);
        
        // Wait for more potential API calls
        console.log('⏳ Monitoring for additional API calls...');
        await page.waitForTimeout(10000);
        
        // Try to trigger API calls by interacting with the page
        console.log('🖱️ Attempting to trigger API calls...');
        
        // Look for buttons, links, or interactive elements
        const interactiveElements = await page.evaluate(() => {
            const buttons = Array.from(document.querySelectorAll('button')).length;
            const links = Array.from(document.querySelectorAll('a')).length;
            const inputs = Array.from(document.querySelectorAll('input')).length;
            const forms = Array.from(document.querySelectorAll('form')).length;
            
            return { buttons, links, inputs, forms };
        });
        
        console.log(`   Interactive elements: ${JSON.stringify(interactiveElements)}`);
        
        // Try clicking some elements to trigger API calls
        try {
            const buttons = await page.$$('button');
            if (buttons.length > 0) {
                console.log(`🖱️ Clicking first button to trigger potential API calls...`);
                await buttons[0].click();
                await page.waitForTimeout(3000);
            }
        } catch (e) {
            console.log('⚠️ Could not click buttons');
        }
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/console-debug-screenshot.png',
            fullPage: true 
        });
        console.log('📸 Screenshot saved: console-debug-screenshot.png');
        
        // Final analysis
        console.log('\n' + '='.repeat(60));
        console.log('📊 CONSOLE ERROR ANALYSIS');
        console.log('='.repeat(60));
        
        const errorMessages = consoleMessages.filter(m => m.type === 'error');
        const warningMessages = consoleMessages.filter(m => m.type === 'warning');
        
        console.log(`📝 Total console messages: ${consoleMessages.length}`);
        console.log(`❌ Errors: ${errorMessages.length}`);
        console.log(`⚠️ Warnings: ${warningMessages.length}`);
        console.log(`🌐 Network errors: ${networkErrors.length}`);
        console.log(`🔌 API-related errors: ${apiErrors.length}`);
        
        if (apiErrors.length > 0) {
            console.log('\n🔌 API ENDPOINT ERRORS FOUND:');
            console.log('─'.repeat(40));
            
            apiErrors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.type?.toUpperCase() || 'CONSOLE'} ERROR:`);
                if (error.url) console.log(`   URL: ${error.url}`);
                if (error.method) console.log(`   Method: ${error.method}`);
                if (error.status) console.log(`   Status: ${error.status} ${error.statusText}`);
                if (error.error) console.log(`   Error: ${error.error}`);
                if (error.text) console.log(`   Message: ${error.text}`);
                console.log('');
            });
            
            // Provide fixes for common API errors
            console.log('🔧 SUGGESTED FIXES:');
            console.log('─'.repeat(20));
            
            apiErrors.forEach((error, index) => {
                if (error.url && error.url.includes('api.51club.local')) {
                    if (error.status === 404) {
                        console.log(`${index + 1}. 404 Error - API endpoint not found:`);
                        console.log(`   • Check if the API route exists in the backend`);
                        console.log(`   • Verify the URL path is correct`);
                    } else if (error.status === 405) {
                        console.log(`${index + 1}. 405 Error - Method not allowed:`);
                        console.log(`   • Change request method to POST`);
                        console.log(`   • API expects POST, not GET requests`);
                    } else if (error.status === 500) {
                        console.log(`${index + 1}. 500 Error - Server error:`);
                        console.log(`   • Check API server logs`);
                        console.log(`   • Verify database connection`);
                    } else if (error.error && error.error.includes('CORS')) {
                        console.log(`${index + 1}. CORS Error:`);
                        console.log(`   • Add CORS headers to API server`);
                        console.log(`   • Allow origin: http://51club.local`);
                    }
                }
            });
        } else {
            console.log('\n✅ NO API ENDPOINT ERRORS FOUND');
            console.log('   The page loaded without API-related console errors');
        }
        
        if (errorMessages.length > 0) {
            console.log('\n❌ OTHER CONSOLE ERRORS:');
            console.log('─'.repeat(30));
            errorMessages.forEach((error, index) => {
                console.log(`${index + 1}. ${error.text}`);
                if (error.location) {
                    console.log(`   Location: ${error.location.url}:${error.location.lineNumber}`);
                }
            });
        }
        
        console.log('\n🎯 NEXT STEPS:');
        if (apiErrors.length > 0) {
            console.log('1. Fix the identified API endpoint errors');
            console.log('2. Test API endpoints individually');
            console.log('3. Check API server configuration');
            console.log('4. Verify CORS settings');
        } else {
            console.log('✅ No API errors found - page is working correctly');
        }
        
    } catch (error) {
        console.error('❌ Error during page analysis:', error.message);
    } finally {
        // Keep browser open for manual inspection
        console.log('\n🔍 Browser kept open for manual inspection...');
        console.log('Press Ctrl+C to close when done.');
        
        // Wait indefinitely until user closes
        await new Promise(() => {});
    }
}

debugConsoleErrors().catch(console.error);
