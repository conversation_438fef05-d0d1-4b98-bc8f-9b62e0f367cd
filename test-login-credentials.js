#!/usr/bin/env node

/**
 * Test login with specific credentials: 3124003124 / 3124003124
 */

const { chromium } = require('playwright');

async function testLoginCredentials() {
    console.log('🔐 Testing Login Credentials');
    console.log('URL: http://51club.local/#/login');
    console.log('Username: 3124003124');
    console.log('Password: 3124003124');
    console.log('='.repeat(60));
    
    const browser = await chromium.launch({ 
        headless: false,
        devtools: true
    });
    
    const context = await browser.newContext({
        ignoreHTTPSErrors: true
    });
    
    const page = await context.newPage();
    
    // Track all errors and API calls
    const errors = [];
    const apiCalls = [];
    const loginAttempts = [];
    
    // Console monitoring
    page.on('console', msg => {
        if (msg.type() === 'error') {
            errors.push({
                message: msg.text(),
                location: msg.location(),
                timestamp: new Date().toISOString()
            });
            console.log(`❌ [ERROR] ${msg.text()}`);
        } else if (msg.type() === 'warning' && !msg.text().includes('Vue warn')) {
            console.log(`⚠️ [WARNING] ${msg.text()}`);
        }
    });
    
    // Network monitoring
    page.on('request', request => {
        if (request.url().includes('api') || request.url().includes('login') || request.url().includes('auth')) {
            const apiCall = {
                url: request.url(),
                method: request.method(),
                postData: request.postData(),
                timestamp: new Date().toISOString()
            };
            apiCalls.push(apiCall);
            console.log(`🌐 [REQUEST] ${request.method()} ${request.url()}`);
            
            // Check if it's a login request
            if (request.postData() && (request.postData().includes('3124003124') || 
                request.url().includes('login') || request.url().includes('auth'))) {
                loginAttempts.push(apiCall);
                console.log(`🔑 [LOGIN ATTEMPT] ${request.method()} ${request.url()}`);
            }
        }
    });
    
    page.on('response', response => {
        if (response.url().includes('api') || response.url().includes('login') || response.url().includes('auth')) {
            const status = response.status();
            const icon = status >= 200 && status < 300 ? '✅' : 
                        status >= 400 ? '❌' : '⚠️';
            console.log(`${icon} [RESPONSE] ${status} ${response.statusText()} - ${response.url()}`);
            
            // Update API call with response
            const apiCall = apiCalls.find(call => call.url === response.url() && !call.status);
            if (apiCall) {
                apiCall.status = status;
                apiCall.statusText = response.statusText();
            }
        }
    });
    
    try {
        console.log('🌐 Step 1: Navigating to login page...');
        
        const response = await page.goto('http://51club.local/#/login', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Login page loaded: ${response.status()}`);
        
        // Wait for page to fully load
        await page.waitForTimeout(3000);
        
        // Take initial screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/login-page-initial.png',
            fullPage: true 
        });
        console.log('📸 Initial screenshot saved: login-page-initial.png');
        
        // Analyze login form
        console.log('\n🔍 Step 2: Analyzing login form...');
        
        const formAnalysis = await page.evaluate(() => {
            const forms = document.querySelectorAll('form');
            const inputs = document.querySelectorAll('input');
            const usernameInputs = document.querySelectorAll('input[type="text"], input[type="email"], input[name*="user"], input[placeholder*="user"], input[id*="user"]');
            const passwordInputs = document.querySelectorAll('input[type="password"]');
            const submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"], button:contains("Login"), button:contains("Sign"), .login-btn, .submit-btn');
            
            return {
                formsCount: forms.length,
                inputsCount: inputs.length,
                usernameInputsCount: usernameInputs.length,
                passwordInputsCount: passwordInputs.length,
                submitButtonsCount: submitButtons.length,
                pageTitle: document.title,
                hasLoginForm: forms.length > 0 && passwordInputs.length > 0,
                bodyContent: document.body.innerHTML.length
            };
        });
        
        console.log(`   Forms found: ${formAnalysis.formsCount}`);
        console.log(`   Input fields: ${formAnalysis.inputsCount}`);
        console.log(`   Username fields: ${formAnalysis.usernameInputsCount}`);
        console.log(`   Password fields: ${formAnalysis.passwordInputsCount}`);
        console.log(`   Submit buttons: ${formAnalysis.submitButtonsCount}`);
        console.log(`   Has login form: ${formAnalysis.hasLoginForm}`);
        console.log(`   Page title: "${formAnalysis.pageTitle}"`);
        
        if (!formAnalysis.hasLoginForm) {
            console.log('⚠️ No login form detected. Checking if we need to navigate differently...');
            
            // Try to find login links or buttons
            const loginLinks = await page.$$('a[href*="login"], button:has-text("Login"), .login-link');
            if (loginLinks.length > 0) {
                console.log(`Found ${loginLinks.length} login links/buttons. Clicking first one...`);
                await loginLinks[0].click();
                await page.waitForTimeout(3000);
            }
        }
        
        // Step 3: Attempt login
        console.log('\n🔑 Step 3: Attempting login...');
        
        try {
            // Find username field (try multiple selectors)
            const usernameSelectors = [
                'input[type="text"]',
                'input[type="email"]',
                'input[name*="user"]',
                'input[placeholder*="user"]',
                'input[id*="user"]',
                'input[name*="phone"]',
                'input[placeholder*="phone"]',
                'input:first-of-type'
            ];
            
            let usernameField = null;
            for (const selector of usernameSelectors) {
                usernameField = await page.$(selector);
                if (usernameField) {
                    console.log(`   Found username field with selector: ${selector}`);
                    break;
                }
            }
            
            // Find password field
            const passwordField = await page.$('input[type="password"]');
            
            if (usernameField && passwordField) {
                console.log('   ✅ Found both username and password fields');
                
                // Clear and fill username
                await usernameField.click();
                await usernameField.fill('');
                await usernameField.type('3124003124', { delay: 100 });
                console.log('   ✅ Entered username: 3124003124');
                
                // Clear and fill password
                await passwordField.click();
                await passwordField.fill('');
                await passwordField.type('3124003124', { delay: 100 });
                console.log('   ✅ Entered password: 3124003124');
                
                // Take screenshot before submit
                await page.screenshot({ 
                    path: '/mnt/g/clients/casino/51club_space/51club_main/login-form-filled.png',
                    fullPage: true 
                });
                console.log('   📸 Screenshot saved: login-form-filled.png');
                
                // Find and click submit button
                const submitSelectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:has-text("Login")',
                    'button:has-text("Sign")',
                    '.login-btn',
                    '.submit-btn',
                    'form button',
                    'button:last-of-type'
                ];
                
                let submitButton = null;
                for (const selector of submitSelectors) {
                    submitButton = await page.$(selector);
                    if (submitButton) {
                        console.log(`   Found submit button with selector: ${selector}`);
                        break;
                    }
                }
                
                if (submitButton) {
                    console.log('   🔄 Clicking submit button...');
                    
                    // Wait for potential login response
                    const loginResponsePromise = page.waitForResponse(response => {
                        return response.url().includes('login') || 
                               response.url().includes('auth') || 
                               response.url().includes('signin') ||
                               (response.request().postData() && response.request().postData().includes('3124003124'));
                    }, { timeout: 10000 }).catch(() => null);
                    
                    await submitButton.click();
                    
                    // Wait for response or timeout
                    const loginResponse = await loginResponsePromise;
                    
                    if (loginResponse) {
                        console.log(`   📊 Login response: ${loginResponse.status()} ${loginResponse.statusText()}`);
                        
                        // Try to get response body
                        try {
                            const responseBody = await loginResponse.text();
                            console.log(`   📄 Response body: ${responseBody.substring(0, 200)}...`);
                        } catch (e) {
                            console.log('   Could not read response body');
                        }
                    }
                    
                    // Wait for potential redirect or page change
                    await page.waitForTimeout(5000);
                    
                    // Check current URL
                    const currentUrl = page.url();
                    console.log(`   📍 Current URL after login: ${currentUrl}`);
                    
                    // Check if login was successful (URL change or content change)
                    const loginSuccess = !currentUrl.includes('login') || currentUrl !== 'http://51club.local/#/login';
                    
                    if (loginSuccess) {
                        console.log('   ✅ Login appears successful (URL changed)');
                    } else {
                        console.log('   ⚠️ Still on login page - checking for error messages');
                        
                        // Look for error messages
                        const errorMessages = await page.evaluate(() => {
                            const errorSelectors = [
                                '.error', '.alert', '.warning', '.message',
                                '[class*="error"]', '[class*="alert"]', '[class*="warning"]'
                            ];
                            
                            const errors = [];
                            errorSelectors.forEach(selector => {
                                const elements = document.querySelectorAll(selector);
                                elements.forEach(el => {
                                    if (el.textContent.trim()) {
                                        errors.push(el.textContent.trim());
                                    }
                                });
                            });
                            
                            return errors;
                        });
                        
                        if (errorMessages.length > 0) {
                            console.log('   ❌ Error messages found:');
                            errorMessages.forEach((msg, i) => {
                                console.log(`      ${i + 1}. ${msg}`);
                            });
                        }
                    }
                    
                } else {
                    console.log('   ❌ Could not find submit button');
                }
                
            } else {
                console.log('   ❌ Could not find username or password fields');
                console.log(`      Username field: ${usernameField ? 'Found' : 'Not found'}`);
                console.log(`      Password field: ${passwordField ? 'Found' : 'Not found'}`);
            }
            
        } catch (loginError) {
            console.log(`   ❌ Login attempt error: ${loginError.message}`);
        }
        
        // Take final screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/login-final-result.png',
            fullPage: true 
        });
        console.log('📸 Final screenshot saved: login-final-result.png');
        
        // Analysis
        console.log('\n' + '='.repeat(60));
        console.log('📊 LOGIN TEST ANALYSIS');
        console.log('='.repeat(60));
        
        console.log(`🔑 Login attempts made: ${loginAttempts.length}`);
        console.log(`🌐 API calls during login: ${apiCalls.length}`);
        console.log(`❌ Console errors: ${errors.length}`);
        
        if (loginAttempts.length > 0) {
            console.log('\n🔑 LOGIN API CALLS:');
            loginAttempts.forEach((attempt, index) => {
                console.log(`   ${index + 1}. ${attempt.method} ${attempt.url}`);
                if (attempt.status) {
                    console.log(`      Status: ${attempt.status} ${attempt.statusText}`);
                }
            });
        }
        
        if (errors.length > 0) {
            console.log('\n❌ ERRORS DETECTED:');
            errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error.message}`);
                if (error.location) {
                    console.log(`      Location: ${error.location.url}:${error.location.lineNumber}`);
                }
            });
        }
        
        // Recommendations
        console.log('\n🔧 RECOMMENDATIONS:');
        if (errors.length === 0 && loginAttempts.length > 0) {
            console.log('✅ Login process appears to be working');
            console.log('   Check if credentials are valid in the database');
        } else if (errors.length > 0) {
            console.log('❌ Errors detected during login process');
            console.log('   Review the errors above and fix accordingly');
        } else {
            console.log('⚠️ No login attempts detected');
            console.log('   Check if login form is properly configured');
        }
        
    } catch (error) {
        console.error('❌ Test error:', error.message);
    } finally {
        console.log('\n🔍 Keeping browser open for manual inspection...');
        console.log('Press Ctrl+C when done reviewing.');
        
        // Keep browser open for manual inspection
        await new Promise(() => {});
    }
}

testLoginCredentials().catch(console.error);
