#!/bin/bash

echo "🔐 Installing SSL Certificates for 51Club Casino..."
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ ERROR: This script requires root privileges."
    echo "Please run with sudo: sudo ./install-ssl-linux.sh"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ ERROR: Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Docker is running."
echo ""
echo "📋 Extracting CA certificate from Docker container..."

# Create temp directory for certificates
mkdir -p /tmp/51club-ssl

# Extract CA certificate from nginx container
if docker cp 51club_nginx:/etc/nginx/ssl/ca.crt /tmp/51club-ssl/ca.crt 2>/dev/null; then
    echo "✅ CA certificate extracted successfully."
    echo ""
    echo "🔧 Installing CA certificate to system trust store..."
    
    # Install CA certificate based on the distribution
    if command -v update-ca-certificates >/dev/null 2>&1; then
        # Debian/Ubuntu based systems
        cp /tmp/51club-ssl/ca.crt /usr/local/share/ca-certificates/51club-ca.crt
        update-ca-certificates
        echo "✅ CA certificate installed for Debian/Ubuntu system."
    elif command -v update-ca-trust >/dev/null 2>&1; then
        # RHEL/CentOS/Fedora based systems
        cp /tmp/51club-ssl/ca.crt /etc/pki/ca-trust/source/anchors/51club-ca.crt
        update-ca-trust
        echo "✅ CA certificate installed for RHEL/CentOS/Fedora system."
    else
        echo "⚠️  Could not automatically install CA certificate."
        echo "💡 Please manually add the certificate to your system's trust store."
        echo "   Certificate location: /tmp/51club-ssl/ca.crt"
    fi
    
    # For browsers that use their own certificate store
    echo ""
    echo "🌐 For Chrome/Chromium browsers:"
    echo "   1. Go to Settings > Privacy and security > Security > Manage certificates"
    echo "   2. Go to 'Authorities' tab"
    echo "   3. Import /tmp/51club-ssl/ca.crt"
    echo ""
    echo "🦊 For Firefox:"
    echo "   1. Go to Settings > Privacy & Security > Certificates > View Certificates"
    echo "   2. Go to 'Authorities' tab"
    echo "   3. Import /tmp/51club-ssl/ca.crt"
    echo ""
    
    echo "🎉 SSL certificates are now trusted by the system!"
    echo ""
    echo "🌐 You can now access your services without SSL warnings:"
    echo "  - Main Site: https://51club.local"
    echo "  - API: https://api.51club.local"
    echo "  - Games API: https://games.51club.local"
    echo "  - Admin Panel: https://admin.51club.local"
    echo "  - phpMyAdmin: https://db.51club.local"
    echo ""
    echo "💡 You may need to restart your browser for changes to take effect."
    
    # Clean up
    rm -rf /tmp/51club-ssl
    
else
    echo "❌ Failed to extract CA certificate."
    echo "💡 Make sure the nginx container is running: docker-compose up -d"
    exit 1
fi

echo ""
