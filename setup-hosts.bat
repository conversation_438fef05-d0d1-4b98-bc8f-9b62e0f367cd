@echo off
echo Setting up hosts file for 51Club Casino services...
echo.
echo This script will add the following entries to your Windows hosts file:
echo 127.0.0.1 51club.local
echo 127.0.0.1 www.51club.local
echo 127.0.0.1 api.51club.local
echo 127.0.0.1 games.51club.local
echo 127.0.0.1 admin.51club.local
echo 127.0.0.1 db.51club.local
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Administrative privileges confirmed.
) else (
    echo ERROR: This script requires administrative privileges.
    echo Please run as Administrator.
    pause
    exit /b 1
)

REM Backup current hosts file
copy "%SystemRoot%\System32\drivers\etc\hosts" "%SystemRoot%\System32\drivers\etc\hosts.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%" >nul

REM Add entries to hosts file
echo. >> "%SystemRoot%\System32\drivers\etc\hosts"
echo # 51Club Casino Services >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1 51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1 www.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1 api.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1 games.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1 admin.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1 db.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"

echo.
echo Hosts file updated successfully!
echo.
echo You can now access your services at:
echo - Main Site: https://51club.local
echo - API: https://api.51club.local
echo - Games API: https://games.51club.local
echo - Admin Panel: https://admin.51club.local
echo - phpMyAdmin: https://db.51club.local
echo.
echo Note: You may need to restart your browser for changes to take effect.
pause
