#!/usr/bin/env node

/**
 * Test db.51club.local endpoint specifically and diagnose 503 error
 */

const { chromium } = require('playwright');

async function testDbEndpoint() {
    console.log('🔍 Testing db.51club.local endpoint with Playwright...');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        console.log('🌐 Navigating to http://db.51club.local/...');
        
        const response = await page.goto('http://db.51club.local/', {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });
        
        console.log(`📊 Response Status: ${response.status()}`);
        console.log(`📊 Response Status Text: ${response.statusText()}`);
        console.log(`📊 Final URL: ${response.url()}`);
        
        // Get page content
        const content = await page.content();
        console.log(`📊 Content Length: ${content.length} bytes`);
        
        // Check for specific error messages
        if (content.includes('503') || content.includes('Service Temporarily Unavailable')) {
            console.log('❌ 503 Service Temporarily Unavailable detected');
            console.log('📄 Page content preview:');
            console.log(content.substring(0, 500) + '...');
        }
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/db-error-screenshot.png',
            fullPage: true 
        });
        console.log('📸 Screenshot saved: db-error-screenshot.png');
        
        // Get page title
        const title = await page.title();
        console.log(`📊 Page Title: "${title}"`);
        
    } catch (error) {
        console.error('❌ Error testing endpoint:', error.message);
    } finally {
        await browser.close();
    }
}

testDbEndpoint();
