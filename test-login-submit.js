#!/usr/bin/env node

/**
 * Test login with better submit button detection
 */

const { chromium } = require('playwright');

async function testLoginSubmit() {
    console.log('🔐 Testing Login Submit - Enhanced Detection');
    console.log('='.repeat(60));
    
    const browser = await chromium.launch({ 
        headless: false,
        devtools: true
    });
    
    const context = await browser.newContext({
        ignoreHTTPSErrors: true
    });
    
    const page = await context.newPage();
    
    // Track login attempts
    const loginAttempts = [];
    
    page.on('request', request => {
        if (request.method() === 'POST' && 
            (request.url().includes('login') || request.url().includes('auth') || 
             request.postData()?.includes('3124003124'))) {
            console.log(`🔑 [LOGIN REQUEST] ${request.method()} ${request.url()}`);
            loginAttempts.push({
                url: request.url(),
                method: request.method(),
                postData: request.postData()
            });
        }
    });
    
    page.on('response', response => {
        if (loginAttempts.some(attempt => attempt.url === response.url())) {
            console.log(`🔑 [LOGIN RESPONSE] ${response.status()} ${response.statusText()}`);
        }
    });
    
    try {
        console.log('🌐 Loading login page...');
        await page.goto('http://51club.local/#/login', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        await page.waitForTimeout(3000);
        
        console.log('🔍 Analyzing all buttons on the page...');
        
        // Get detailed info about all buttons
        const buttonInfo = await page.evaluate(() => {
            const buttons = Array.from(document.querySelectorAll('button'));
            return buttons.map((btn, index) => ({
                index,
                text: btn.textContent?.trim() || '',
                type: btn.type || '',
                className: btn.className || '',
                id: btn.id || '',
                onclick: btn.onclick ? 'has onclick' : 'no onclick',
                disabled: btn.disabled,
                visible: btn.offsetParent !== null
            }));
        });
        
        console.log('📊 All buttons found:');
        buttonInfo.forEach(btn => {
            console.log(`   ${btn.index}: "${btn.text}" (type: ${btn.type}, class: ${btn.className}, visible: ${btn.visible})`);
        });
        
        // Fill the form first
        console.log('\n🔑 Filling login form...');
        
        const usernameField = await page.$('input[type="text"]');
        const passwordField = await page.$('input[type="password"]');
        
        if (usernameField && passwordField) {
            await usernameField.fill('3124003124');
            await passwordField.fill('3124003124');
            console.log('✅ Form filled with credentials');
            
            // Take screenshot
            await page.screenshot({ 
                path: '/mnt/g/clients/casino/51club_space/51club_main/login-form-ready.png',
                fullPage: true 
            });
            
            // Try different approaches to submit
            console.log('\n🔄 Attempting different submit methods...');
            
            // Method 1: Try each button that might be submit
            const potentialSubmitButtons = buttonInfo.filter(btn => 
                btn.visible && !btn.disabled && (
                    btn.text.toLowerCase().includes('login') ||
                    btn.text.toLowerCase().includes('sign') ||
                    btn.text.toLowerCase().includes('submit') ||
                    btn.text.toLowerCase().includes('登录') ||
                    btn.type === 'submit' ||
                    btn.className.includes('submit') ||
                    btn.className.includes('login')
                )
            );
            
            console.log(`Found ${potentialSubmitButtons.length} potential submit buttons:`);
            potentialSubmitButtons.forEach(btn => {
                console.log(`   Button ${btn.index}: "${btn.text}" (${btn.className})`);
            });
            
            if (potentialSubmitButtons.length > 0) {
                // Try the first potential submit button
                const targetButton = potentialSubmitButtons[0];
                console.log(`🎯 Trying button ${targetButton.index}: "${targetButton.text}"`);
                
                try {
                    const buttonElement = await page.$(`button:nth-of-type(${targetButton.index + 1})`);
                    if (buttonElement) {
                        // Set up response monitoring
                        const responsePromise = page.waitForResponse(response => {
                            return response.request().method() === 'POST' && 
                                   response.url().includes('api');
                        }, { timeout: 10000 }).catch(() => null);
                        
                        await buttonElement.click();
                        console.log('✅ Button clicked');
                        
                        // Wait for response
                        const response = await responsePromise;
                        if (response) {
                            console.log(`📊 API Response: ${response.status()} ${response.statusText()}`);
                            
                            try {
                                const responseText = await response.text();
                                console.log(`📄 Response body: ${responseText}`);
                                
                                // Try to parse JSON
                                try {
                                    const responseJson = JSON.parse(responseText);
                                    console.log('📊 Parsed response:');
                                    console.log(`   Code: ${responseJson.code}`);
                                    console.log(`   Message: ${responseJson.msg || responseJson.message}`);
                                    
                                    if (responseJson.code === 0) {
                                        console.log('✅ Login successful!');
                                    } else {
                                        console.log(`❌ Login failed: ${responseJson.msg || responseJson.message}`);
                                    }
                                } catch (e) {
                                    console.log('Response is not JSON');
                                }
                            } catch (e) {
                                console.log('Could not read response body');
                            }
                        } else {
                            console.log('⚠️ No API response received');
                        }
                        
                        // Wait and check URL change
                        await page.waitForTimeout(3000);
                        const currentUrl = page.url();
                        console.log(`📍 Current URL: ${currentUrl}`);
                        
                        if (!currentUrl.includes('login')) {
                            console.log('✅ Redirected away from login - likely successful');
                        }
                        
                    } else {
                        console.log('❌ Could not find button element');
                    }
                } catch (e) {
                    console.log(`❌ Error clicking button: ${e.message}`);
                }
            } else {
                console.log('❌ No suitable submit buttons found');
                
                // Try alternative: Press Enter on password field
                console.log('🔄 Trying Enter key on password field...');
                await passwordField.press('Enter');
                await page.waitForTimeout(3000);
                
                const currentUrl = page.url();
                console.log(`📍 URL after Enter: ${currentUrl}`);
            }
            
        } else {
            console.log('❌ Could not find username or password fields');
        }
        
        // Final screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/login-final-attempt.png',
            fullPage: true 
        });
        
        console.log('\n📊 FINAL RESULTS:');
        console.log(`🔑 Login attempts made: ${loginAttempts.length}`);
        
        if (loginAttempts.length > 0) {
            console.log('✅ Login request was sent to server');
            loginAttempts.forEach((attempt, i) => {
                console.log(`   ${i + 1}. ${attempt.method} ${attempt.url}`);
            });
        } else {
            console.log('❌ No login requests detected');
            console.log('   Possible issues:');
            console.log('   1. Submit button not found/clicked properly');
            console.log('   2. Form validation preventing submission');
            console.log('   3. JavaScript errors blocking the request');
        }
        
    } catch (error) {
        console.error('❌ Test error:', error.message);
    } finally {
        await browser.close();
    }
}

testLoginSubmit();
