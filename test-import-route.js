#!/usr/bin/env node

/**
 * Test the specific phpMyAdmin import route that's showing 503 error
 */

const { chromium } = require('playwright');

async function testImportRoute() {
    console.log('🔍 Testing phpMyAdmin import route...');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Track network requests
    const requests = [];
    const responses = [];
    
    page.on('request', request => {
        requests.push({
            url: request.url(),
            method: request.method(),
            resourceType: request.resourceType()
        });
    });
    
    page.on('response', response => {
        responses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText()
        });
    });
    
    try {
        // First test the main phpMyAdmin page
        console.log('🌐 Step 1: Loading main phpMyAdmin page...');
        const mainResponse = await page.goto('http://db.51club.local/', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        console.log(`📊 Main page: ${mainResponse.status()} ${mainResponse.statusText()}`);
        
        // Wait for page to load
        await page.waitForTimeout(2000);
        
        // Now test the specific import route
        console.log('🌐 Step 2: Testing import route...');
        const importUrl = 'http://db.51club.local/index.php?route=/database/import&db=casino_db';
        
        const importResponse = await page.goto(importUrl, {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });
        
        console.log(`📊 Import route: ${importResponse.status()} ${importResponse.statusText()}`);
        console.log(`📊 Final URL: ${importResponse.url()}`);
        
        // Get page content
        const content = await page.content();
        const title = await page.title();
        
        console.log(`📊 Page Title: "${title}"`);
        console.log(`📊 Content Length: ${content.length} bytes`);
        
        // Check for specific error messages
        if (content.includes('503') || content.includes('Service Temporarily Unavailable')) {
            console.log('❌ 503 Service Temporarily Unavailable detected');
            console.log('📄 Error content preview:');
            console.log(content.substring(0, 1000));
        } else if (content.includes('Import') || content.includes('upload')) {
            console.log('✅ Import page loaded successfully');
        } else if (content.includes('phpMyAdmin')) {
            console.log('✅ phpMyAdmin page loaded (may have redirected)');
        }
        
        // Check for import-specific elements
        const hasImportForm = await page.$('form[enctype="multipart/form-data"]') !== null;
        const hasFileInput = await page.$('input[type="file"]') !== null;
        const hasImportButton = content.includes('Import') || content.includes('Go');
        
        console.log(`📊 Has import form: ${hasImportForm}`);
        console.log(`📊 Has file input: ${hasFileInput}`);
        console.log(`📊 Has import button: ${hasImportButton}`);
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/import-route-test.png',
            fullPage: true 
        });
        console.log('📸 Screenshot saved: import-route-test.png');
        
        // Analyze failed requests
        const failedResponses = responses.filter(r => r.status >= 400);
        if (failedResponses.length > 0) {
            console.log('\n❌ Failed requests:');
            failedResponses.forEach(r => {
                console.log(`   ${r.status} ${r.statusText} - ${r.url}`);
            });
        } else {
            console.log('\n✅ All requests successful');
        }
        
        // Test result
        if (importResponse.status() === 200 && (hasImportForm || hasFileInput)) {
            console.log('\n🎉 SUCCESS: Import route is working!');
        } else if (importResponse.status() === 503) {
            console.log('\n❌ CONFIRMED: 503 error on import route');
            console.log('   This is likely due to nginx rate limiting');
        } else {
            console.log('\n⚠️  PARTIAL: Page loads but import functionality unclear');
        }
        
    } catch (error) {
        console.error('❌ Error testing import route:', error.message);
    } finally {
        await browser.close();
    }
}

testImportRoute();
