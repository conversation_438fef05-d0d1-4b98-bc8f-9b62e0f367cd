# 51Club Casino - Docker Setup

This project contains a complete Docker setup for the 51Club Casino platform with clean URL access without port numbers.

## Services Overview

- **Main Website** (`main`): PHP/Apache - Main frontend application
- **API Service** (`api`): PHP/Apache - Backend API endpoints
- **Games API** (`api_games`): Node.js - Game integration API
- **Admin Panel** (`adminx`): PHP/Apache - Administrative interface
- **phpMyAdmin** (`phpmyadmin`): Database management interface
- **MySQL Database** (`mysql`): Database server
- **Nginx** (`nginx`): Reverse proxy with SSL termination

## Clean URL Access

After setup, you can access services using these clean URLs:

- **Main Site**: https://51club.local
- **API**: https://api.51club.local
- **Games API**: https://games.51club.local
- **Admin Panel**: https://admin.51club.local
- **phpMyAdmin**: https://db.51club.local

## Prerequisites

1. **Docker Desktop** installed on Windows 10
2. **WSL2** with Ubuntu enabled
3. **Git** for version control

## Setup Instructions

### Step 1: Clone and Navigate
```bash
cd /mnt/g/clients/casino/51club_space/51club_main
```

### Step 2: Setup Hosts File

#### For Windows (Run as Administrator):
```cmd
setup-hosts.bat
```

#### For WSL/Linux:
```bash
sudo ./setup-hosts.sh
```

### Step 3: Build and Start Services
```bash
# Build all services
docker-compose build

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f
```

### Step 4: Verify Services
```bash
# Check running containers
docker-compose ps

# Check service health
curl -k https://51club.local
curl -k https://api.51club.local
curl -k https://games.51club.local
curl -k https://admin.51club.local
curl -k https://db.51club.local
```

## Service Configuration

### Database Configuration
- **Host**: mysql
- **Port**: 3306
- **Database**: casino_db
- **Username**: casino_user
- **Password**: casino_pass_2024
- **Root Password**: casino_root_2024

### SSL Certificates
Self-signed SSL certificates are automatically generated for development. For production, replace with valid certificates in `docker/ssl/`.

### Environment Variables
Key environment variables are configured in `docker-compose.yml`. Modify as needed for your environment.

## Development Workflow

### Starting Services
```bash
docker-compose up -d
```

### Stopping Services
```bash
docker-compose down
```

### Rebuilding After Changes
```bash
# Rebuild specific service
docker-compose build main
docker-compose up -d main

# Rebuild all services
docker-compose build
docker-compose up -d
```

### Viewing Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f main
docker-compose logs -f nginx
```

### Database Management
```bash
# Access MySQL directly
docker-compose exec mysql mysql -u root -p

# Import database
docker-compose exec mysql mysql -u root -p casino_db < 51club_space.sql
```

## File Structure
```
51club_main/
├── docker/
│   ├── nginx/
│   │   ├── Dockerfile
│   │   ├── nginx.conf
│   │   └── conf.d/default.conf
│   ├── main/
│   │   ├── Dockerfile
│   │   ├── apache-config.conf
│   │   └── php.ini
│   ├── api/
│   │   ├── Dockerfile
│   │   ├── apache-config.conf
│   │   └── php.ini
│   ├── api_games/
│   │   ├── Dockerfile
│   │   ├── package.json
│   │   └── yarn.lock
│   ├── adminx/
│   │   ├── Dockerfile
│   │   ├── apache-config.conf
│   │   └── php.ini
│   └── ssl/
├── main/          # Main website files
├── api/           # API files
├── api_games/     # Games API files
├── adminx/        # Admin panel files
├── docker-compose.yml
├── setup-hosts.bat
├── setup-hosts.sh
└── README.md
```

## Security Features

- SSL/TLS encryption for all services
- Rate limiting on API endpoints
- Security headers configured
- File access restrictions
- Non-root user for Node.js service
- Database access controls

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 80, 443, and 3306 are available
2. **Permission issues**: Check file permissions in mounted volumes
3. **DNS resolution**: Verify hosts file entries
4. **SSL warnings**: Accept self-signed certificates in browser

### Useful Commands
```bash
# Check container status
docker-compose ps

# Restart specific service
docker-compose restart nginx

# View container resource usage
docker stats

# Clean up unused resources
docker system prune

# Access container shell
docker-compose exec main bash
docker-compose exec mysql bash
```

### Log Locations
- Nginx: `docker/nginx/logs/`
- Apache (Main): `docker/main/logs/`
- Apache (API): `docker/api/logs/`
- Apache (AdminX): `docker/adminx/logs/`
- Node.js (Games): `docker/api_games/logs/`
- MySQL: `docker/mysql/logs/`

## Production Deployment

For production deployment:

1. Replace self-signed certificates with valid SSL certificates
2. Update database passwords and credentials
3. Configure proper backup strategies
4. Set up monitoring and logging
5. Configure firewall rules
6. Use environment-specific configuration files

## Support

For issues and questions:
1. Check the logs: `docker-compose logs -f [service_name]`
2. Verify service health: `docker-compose ps`
3. Check network connectivity: `docker network ls`
4. Review configuration files for syntax errors
