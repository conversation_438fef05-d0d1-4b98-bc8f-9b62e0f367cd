# Docker WSL Integration Setup Guide

## Step 1: Enable Docker Desktop WSL Integration

1. **Open Docker Desktop on Windows**
2. **Click on Settings (gear icon)**
3. **Go to Resources → WSL Integration**
4. **Enable "Enable integration with my default WSL distro"**
5. **Enable integration with your specific Ubuntu distro**
6. **Click "Apply & Restart"**

## Step 2: Restart WSL

Open PowerShell as Administrator and run:
```powershell
wsl --shutdown
wsl
```

## Step 3: Verify Docker in WSL

In your WSL terminal, run:
```bash
docker --version
docker compose --version
```

You should see version information for both commands.

## Step 4: Run the Services

Once Docker is working in WSL, run:
```bash
cd /mnt/g/clients/casino/51club_space/51club_main
./start.sh
```

## Alternative: Run from Windows

If WSL integration doesn't work, you can run from Windows PowerShell:
```powershell
cd G:\clients\casino\51club_space\51club_main
docker-compose build
docker-compose up -d
```

## Troubleshooting

If you still have issues:

1. **Restart Docker Desktop completely**
2. **Restart your computer**
3. **Check Windows Subsystem for Linux is enabled**
4. **Ensure WSL 2 is the default version**

```powershell
wsl --set-default-version 2
```
