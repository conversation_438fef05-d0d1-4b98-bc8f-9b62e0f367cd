#!/usr/bin/env node

/**
 * Test the fixed import route
 */

const { chromium } = require('playwright');

async function testImportFixed() {
    console.log('🔧 Testing fixed phpMyAdmin import route...');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Track all requests
    const allRequests = [];
    const allResponses = [];
    
    page.on('request', request => {
        allRequests.push({
            url: request.url(),
            method: request.method()
        });
    });
    
    page.on('response', response => {
        allResponses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText()
        });
    });
    
    try {
        console.log('🌐 Testing import route directly...');
        const importUrl = 'http://db.51club.local/index.php?route=/database/import&db=casino_db';
        
        const response = await page.goto(importUrl, {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Import page: ${response.status()} ${response.statusText()}`);
        
        // Wait for all AJAX requests to complete
        await page.waitForTimeout(3000);
        
        // Get page info
        const pageInfo = await page.evaluate(() => {
            return {
                title: document.title,
                hasImportForm: document.querySelector('form[enctype="multipart/form-data"]') !== null,
                hasFileInput: document.querySelector('input[type="file"]') !== null,
                hasImportButton: document.body.innerHTML.includes('Import') || document.body.innerHTML.includes('Go'),
                hasErrorMessage: document.body.innerHTML.includes('503') || document.body.innerHTML.includes('Service Temporarily Unavailable'),
                contentLength: document.body.innerHTML.length
            };
        });
        
        console.log('📊 Page Analysis:');
        console.log(`   Title: "${pageInfo.title}"`);
        console.log(`   Has import form: ${pageInfo.hasImportForm}`);
        console.log(`   Has file input: ${pageInfo.hasFileInput}`);
        console.log(`   Has import button: ${pageInfo.hasImportButton}`);
        console.log(`   Has error message: ${pageInfo.hasErrorMessage}`);
        console.log(`   Content length: ${pageInfo.contentLength} chars`);
        
        // Check for failed requests
        const failedResponses = allResponses.filter(r => r.status >= 400);
        const successfulResponses = allResponses.filter(r => r.status >= 200 && r.status < 300);
        
        console.log('\n📊 Network Analysis:');
        console.log(`   Total requests: ${allRequests.length}`);
        console.log(`   Successful responses: ${successfulResponses.length}`);
        console.log(`   Failed responses: ${failedResponses.length}`);
        
        if (failedResponses.length > 0) {
            console.log('\n❌ Failed requests:');
            failedResponses.forEach(r => {
                console.log(`   ${r.status} ${r.statusText} - ${r.url}`);
            });
        } else {
            console.log('\n✅ All requests successful!');
        }
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/import-fixed-screenshot.png',
            fullPage: true 
        });
        console.log('\n📸 Screenshot saved: import-fixed-screenshot.png');
        
        // Final assessment
        const isFullyFixed = (
            response.status() === 200 &&
            pageInfo.hasImportForm &&
            pageInfo.hasFileInput &&
            !pageInfo.hasErrorMessage &&
            failedResponses.length === 0
        );
        
        const isMostlyFixed = (
            response.status() === 200 &&
            pageInfo.hasImportForm &&
            !pageInfo.hasErrorMessage
        );
        
        console.log('\n🎯 FINAL RESULT:');
        if (isFullyFixed) {
            console.log('🎉 PERFECT: Import route is fully functional!');
            console.log('✅ No 503 errors');
            console.log('✅ All AJAX requests working');
            console.log('✅ Import form fully loaded');
            console.log('✅ Ready to import database files');
        } else if (isMostlyFixed) {
            console.log('✅ GOOD: Import route is working with minor issues');
            console.log('✅ Main functionality available');
            console.log('✅ No 503 errors on main page');
        } else {
            console.log('❌ STILL ISSUES: Import route has problems');
            if (pageInfo.hasErrorMessage) console.log('   - Still showing error messages');
            if (!pageInfo.hasImportForm) console.log('   - Import form not loading');
        }
        
        console.log('\n🌐 Test URL: http://db.51club.local/index.php?route=/database/import&db=casino_db');
        
    } catch (error) {
        console.error('❌ Error testing import route:', error.message);
    } finally {
        await browser.close();
    }
}

testImportFixed();
