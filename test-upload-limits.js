#!/usr/bin/env node

/**
 * Test phpMyAdmin upload limits and 413 error
 */

const { chromium } = require('playwright');

async function testUploadLimits() {
    console.log('🔍 Testing phpMyAdmin upload limits (413 error)...');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        console.log('🌐 Loading import page...');
        const importUrl = 'http://db.51club.local/index.php?route=/import';
        
        const response = await page.goto(importUrl, {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Import page: ${response.status()} ${response.statusText()}`);
        
        if (response.status() === 413) {
            console.log('❌ CONFIRMED: 413 Request Entity Too Large error');
        }
        
        // Check for upload limit information on the page
        const uploadInfo = await page.evaluate(() => {
            const body = document.body.innerHTML;
            const hasUploadLimit = body.includes('upload_max_filesize') || body.includes('post_max_size');
            const hasMaxFileSize = body.includes('MAX_FILE_SIZE');
            
            // Try to find upload limit values
            const uploadLimitMatch = body.match(/upload_max_filesize[:\s]*([0-9]+[KMG]?)/i);
            const postLimitMatch = body.match(/post_max_size[:\s]*([0-9]+[KMG]?)/i);
            
            return {
                hasUploadLimit,
                hasMaxFileSize,
                uploadLimit: uploadLimitMatch ? uploadLimitMatch[1] : 'not found',
                postLimit: postLimitMatch ? postLimitMatch[1] : 'not found',
                contentLength: body.length,
                title: document.title
            };
        });
        
        console.log('📊 Upload Limit Analysis:');
        console.log(`   Page title: "${uploadInfo.title}"`);
        console.log(`   Has upload limit info: ${uploadInfo.hasUploadLimit}`);
        console.log(`   Upload max filesize: ${uploadInfo.uploadLimit}`);
        console.log(`   Post max size: ${uploadInfo.postLimit}`);
        console.log(`   Content length: ${uploadInfo.contentLength} chars`);
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/upload-limits-test.png',
            fullPage: true 
        });
        console.log('📸 Screenshot saved: upload-limits-test.png');
        
        // Check if page shows 413 error content
        const content = await page.content();
        if (content.includes('413') || content.includes('Request Entity Too Large')) {
            console.log('❌ 413 error detected in page content');
            console.log('📄 Error content preview:');
            console.log(content.substring(0, 1000));
        }
        
    } catch (error) {
        console.error('❌ Error testing upload limits:', error.message);
    } finally {
        await browser.close();
    }
}

testUploadLimits();
