#!/bin/bash

echo "🎰 51Club Casino - Complete Service Startup"
echo "=========================================="
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Docker availability
echo "🔍 Checking Docker availability..."
if command_exists docker; then
    echo "✅ Docker found: $(docker --version)"
else
    echo "❌ Docker not found in WSL"
    echo "📋 Please follow these steps:"
    echo "   1. Open Docker Desktop on Windows"
    echo "   2. Go to Settings → Resources → WSL Integration"
    echo "   3. Enable integration with your WSL distro"
    echo "   4. Click 'Apply & Restart'"
    echo "   5. Restart WSL: wsl --shutdown && wsl"
    echo ""
    echo "📖 See setup-docker-wsl.md for detailed instructions"
    exit 1
fi

# Check Docker Compose
if command_exists docker-compose; then
    COMPOSE_CMD="docker-compose"
    echo "✅ Docker Compose found: $(docker-compose --version)"
elif docker compose version >/dev/null 2>&1; then
    COMPOSE_CMD="docker compose"
    echo "✅ Docker Compose (plugin) found: $(docker compose version)"
else
    echo "❌ Docker Compose not available"
    exit 1
fi

echo ""
echo "🏗️  Building all services..."
echo "=============================="
$COMPOSE_CMD build --no-cache

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please check the error messages above."
    exit 1
fi

echo ""
echo "🚀 Starting all services..."
echo "============================"
$COMPOSE_CMD up -d

if [ $? -ne 0 ]; then
    echo "❌ Failed to start services. Please check the error messages above."
    exit 1
fi

echo ""
echo "⏳ Waiting for services to initialize..."
sleep 20

echo ""
echo "📊 Service Status:"
echo "=================="
$COMPOSE_CMD ps

echo ""
echo "🔐 Checking SSL certificates..."
echo "==============================="
if docker exec 51club_nginx test -f /etc/nginx/ssl/51club.local.crt 2>/dev/null; then
    echo "✅ SSL certificates are ready"
else
    echo "⚠️  Generating SSL certificates..."
    docker exec 51club_nginx /usr/local/bin/generate-ssl.sh
fi

echo ""
echo "🌐 Your 51Club Casino is now running!"
echo "====================================="
echo ""
echo "🔗 Access your services:"
echo "  🏠 Main Site:    https://51club.local"
echo "  🔌 API:          https://api.51club.local"
echo "  🎮 Games API:    https://games.51club.local"
echo "  ⚙️  Admin Panel:  https://admin.51club.local"
echo "  🗄️  phpMyAdmin:  https://db.51club.local"
echo ""
echo "🔐 SSL Security Features:"
echo "  ✅ All endpoints secured with SSL/TLS"
echo "  ✅ HTTP to HTTPS redirects"
echo "  ✅ Security headers configured"
echo "  ✅ Rate limiting enabled"
echo ""
echo "💡 To install SSL certificates (remove browser warnings):"
echo "  Windows: Run 'install-ssl-windows.bat' as Administrator"
echo "  Linux:   Run 'sudo ./install-ssl-linux.sh'"
echo ""
echo "📝 Useful commands:"
echo "  📋 View logs:        $COMPOSE_CMD logs -f"
echo "  🛑 Stop services:    $COMPOSE_CMD down"
echo "  🔄 Restart service:  $COMPOSE_CMD restart [service_name]"
echo "  📊 Service status:   $COMPOSE_CMD ps"
echo ""
echo "🎉 Setup complete! Happy gaming! 🎲"
