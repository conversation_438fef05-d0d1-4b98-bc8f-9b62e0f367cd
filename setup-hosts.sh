#!/bin/bash

echo "Setting up hosts file for 51Club Casino services..."
echo ""
echo "This script will add the following entries to your hosts file:"
echo "127.0.0.1 51club.local"
echo "127.0.0.1 www.51club.local"
echo "127.0.0.1 api.51club.local"
echo "127.0.0.1 games.51club.local"
echo "127.0.0.1 admin.51club.local"
echo "127.0.0.1 db.51club.local"
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "ERROR: This script requires root privileges."
    echo "Please run with sudo: sudo ./setup-hosts.sh"
    exit 1
fi

# Backup current hosts file
cp /etc/hosts /etc/hosts.backup.$(date +%Y%m%d_%H%M%S)

# Check if entries already exist
if grep -q "51club.local" /etc/hosts; then
    echo "Entries already exist in hosts file. Removing old entries..."
    sed -i '/# 51Club Casino Services/,/db\.51club\.local/d' /etc/hosts
fi

# Add entries to hosts file
cat >> /etc/hosts << EOF

# 51Club Casino Services
127.0.0.1 51club.local
127.0.0.1 www.51club.local
127.0.0.1 api.51club.local
127.0.0.1 games.51club.local
127.0.0.1 admin.51club.local
127.0.0.1 db.51club.local
EOF

echo ""
echo "Hosts file updated successfully!"
echo ""
echo "You can now access your services at:"
echo "- Main Site: https://51club.local"
echo "- API: https://api.51club.local"
echo "- Games API: https://games.51club.local"
echo "- Admin Panel: https://admin.51club.local"
echo "- phpMyAdmin: https://db.51club.local"
echo ""
echo "Note: You may need to restart your browser for changes to take effect."
