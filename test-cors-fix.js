#!/usr/bin/env node

/**
 * Test CORS fix with the actual frontend
 */

const { chromium } = require('playwright');

async function testCorsFix() {
    console.log('🔧 Testing CORS Fix - Frontend Integration');
    console.log('='.repeat(50));
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext({
        ignoreHTTPSErrors: true
    });
    const page = await context.newPage();
    
    // Track API requests
    const apiRequests = [];
    const corsErrors = [];
    
    page.on('console', msg => {
        if (msg.type() === 'error' && msg.text().includes('CORS')) {
            corsErrors.push({
                message: msg.text(),
                timestamp: new Date().toISOString()
            });
            console.log(`❌ CORS Error: ${msg.text()}`);
        } else if (msg.type() === 'error') {
            console.log(`❌ Error: ${msg.text()}`);
        }
    });
    
    page.on('response', response => {
        if (response.url().includes('api.51club.local')) {
            apiRequests.push({
                url: response.url(),
                status: response.status(),
                statusText: response.statusText(),
                timestamp: new Date().toISOString()
            });
            
            const icon = response.status() >= 200 && response.status() < 300 ? '✅' : '❌';
            console.log(`${icon} API: ${response.status()} ${response.url()}`);
        }
    });
    
    try {
        console.log('🌐 Loading http://51club.local/#/...');
        
        const response = await page.goto('http://51club.local/#/', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Page loaded: ${response.status()}`);
        
        // Wait for API calls to complete
        console.log('⏳ Waiting for API calls to complete...');
        await page.waitForTimeout(10000);
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/cors-fix-test.png',
            fullPage: true 
        });
        console.log('📸 Screenshot saved: cors-fix-test.png');
        
        // Analysis
        console.log('\n' + '='.repeat(50));
        console.log('📊 CORS FIX ANALYSIS');
        console.log('='.repeat(50));
        
        const successfulApiCalls = apiRequests.filter(r => r.status >= 200 && r.status < 300);
        const failedApiCalls = apiRequests.filter(r => r.status >= 400);
        
        console.log(`📈 Total API requests: ${apiRequests.length}`);
        console.log(`✅ Successful API calls: ${successfulApiCalls.length}`);
        console.log(`❌ Failed API calls: ${failedApiCalls.length}`);
        console.log(`🚫 CORS errors: ${corsErrors.length}`);
        
        if (corsErrors.length === 0) {
            console.log('\n🎉 SUCCESS: CORS errors have been FIXED!');
            console.log('✅ No more "ar-origin is not allowed" errors');
            console.log('✅ Frontend can now communicate with API');
        } else {
            console.log('\n⚠️ PARTIAL FIX: Some CORS errors remain');
            corsErrors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.message}`);
            });
        }
        
        if (successfulApiCalls.length > 0) {
            console.log('\n✅ Working API endpoints:');
            successfulApiCalls.forEach(call => {
                const endpoint = call.url.split('/').pop();
                console.log(`   • ${endpoint} - ${call.status}`);
            });
        }
        
        if (failedApiCalls.length > 0) {
            console.log('\n❌ Failed API endpoints:');
            failedApiCalls.forEach(call => {
                const endpoint = call.url.split('/').pop();
                console.log(`   • ${endpoint} - ${call.status} ${call.statusText}`);
            });
        }
        
        // Overall assessment
        const fixSuccess = corsErrors.length === 0 && successfulApiCalls.length > 0;
        
        console.log('\n🎯 FINAL RESULT:');
        if (fixSuccess) {
            console.log('🎉 EXCELLENT: CORS issue completely resolved!');
            console.log('✅ Frontend-API communication working');
            console.log('✅ All API endpoints accessible');
            console.log('✅ No CORS policy violations');
        } else if (corsErrors.length === 0) {
            console.log('✅ GOOD: CORS errors fixed, but API may need parameter tuning');
        } else {
            console.log('⚠️ PARTIAL: Some CORS issues may remain');
        }
        
    } catch (error) {
        console.error('❌ Test error:', error.message);
    } finally {
        await browser.close();
    }
}

testCorsFix();
