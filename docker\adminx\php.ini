[PHP]
; Performance settings
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 100M
upload_max_filesize = 100M
max_file_uploads = 20

; Error reporting
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/apache2/php_errors.log

; Session settings
session.save_handler = files
session.save_path = "/tmp"
session.gc_maxlifetime = 3600
session.cookie_httponly = On
session.cookie_secure = Off
session.use_strict_mode = On

; Security settings
expose_php = Off
allow_url_fopen = On
allow_url_include = Off

; Date settings
date.timezone = "UTC"

; OPcache settings
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
