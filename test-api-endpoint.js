#!/usr/bin/env node

/**
 * Test the specific API endpoint: /api/webapi/GetHomeSettings
 */

const { chromium } = require('playwright');

async function testApiEndpoint() {
    console.log('🔍 Testing API endpoint: /api/webapi/GetHomeSettings');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext({
        ignoreHTTPSErrors: true // Accept self-signed certificates
    });
    const page = await context.newPage();
    
    // Track network requests
    const requests = [];
    const responses = [];
    
    page.on('request', request => {
        requests.push({
            url: request.url(),
            method: request.method(),
            headers: request.headers(),
            postData: request.postData()
        });
    });
    
    page.on('response', response => {
        responses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText(),
            headers: response.headers()
        });
    });
    
    try {
        console.log('🌐 Testing different variations of the API endpoint...');
        
        const baseUrls = [
            'http://api.51club.local',
            'https://api.51club.local'
        ];
        
        const endpoints = [
            '/api/webapi/GetHomeSettings',
            '/api/webapi/gethomesettings',
            '/webapi/GetHomeSettings',
            '/GetHomeSettings'
        ];
        
        for (const baseUrl of baseUrls) {
            console.log(`\n📊 Testing ${baseUrl}:`);
            console.log('─'.repeat(50));
            
            for (const endpoint of endpoints) {
                const fullUrl = baseUrl + endpoint;
                
                try {
                    console.log(`🔗 GET ${endpoint}`);
                    const response = await page.goto(fullUrl, {
                        waitUntil: 'domcontentloaded',
                        timeout: 10000
                    });
                    
                    const content = await page.content();
                    const isJson = content.includes('{') && content.includes('}');
                    
                    console.log(`   Status: ${response.status()} ${response.statusText()}`);
                    
                    if (isJson) {
                        try {
                            const jsonContent = JSON.parse(content.replace(/<[^>]*>/g, ''));
                            console.log(`   Response: ${JSON.stringify(jsonContent, null, 2).substring(0, 200)}...`);
                        } catch (e) {
                            console.log(`   Content: ${content.substring(0, 100)}...`);
                        }
                    } else {
                        console.log(`   Content: ${content.substring(0, 100)}...`);
                    }
                    
                } catch (error) {
                    console.log(`   Error: ${error.message}`);
                }
                
                await page.waitForTimeout(500); // Small delay between requests
            }
        }
        
        // Test with different HTTP methods using fetch
        console.log('\n🔄 Testing different HTTP methods:');
        console.log('─'.repeat(50));
        
        const testUrl = 'http://api.51club.local/api/webapi/GetHomeSettings';
        
        const methods = ['GET', 'POST', 'PUT', 'OPTIONS'];
        
        for (const method of methods) {
            try {
                const result = await page.evaluate(async (url, httpMethod) => {
                    try {
                        const response = await fetch(url, {
                            method: httpMethod,
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            }
                        });
                        
                        const text = await response.text();
                        return {
                            status: response.status,
                            statusText: response.statusText,
                            content: text,
                            headers: Object.fromEntries(response.headers.entries())
                        };
                    } catch (error) {
                        return { error: error.message };
                    }
                }, testUrl, method);
                
                console.log(`🔗 ${method} request:`);
                if (result.error) {
                    console.log(`   Error: ${result.error}`);
                } else {
                    console.log(`   Status: ${result.status} ${result.statusText}`);
                    console.log(`   Content: ${result.content.substring(0, 100)}...`);
                }
                
            } catch (error) {
                console.log(`🔗 ${method} request: Error - ${error.message}`);
            }
        }
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/api-endpoint-test.png',
            fullPage: true 
        });
        console.log('\n📸 Screenshot saved: api-endpoint-test.png');
        
        // Check for CORS issues
        const corsErrors = responses.filter(r => 
            r.status === 0 || 
            r.headers['access-control-allow-origin'] === undefined
        );
        
        console.log('\n📊 Analysis:');
        console.log('─'.repeat(20));
        console.log(`Total requests: ${requests.length}`);
        console.log(`Total responses: ${responses.length}`);
        console.log(`CORS issues: ${corsErrors.length}`);
        
        // Check if API is working
        const workingResponses = responses.filter(r => 
            r.status === 200 && r.url.includes('GetHomeSettings')
        );
        
        if (workingResponses.length > 0) {
            console.log('✅ API endpoint is accessible');
        } else {
            console.log('⚠️  API endpoint may have issues');
        }
        
    } catch (error) {
        console.error('❌ Error testing API endpoint:', error.message);
    } finally {
        await browser.close();
    }
}

testApiEndpoint();
