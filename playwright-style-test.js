#!/usr/bin/env node

/**
 * 51Club Casino - Playwright-style Endpoint Testing
 * This script tests all endpoints like <PERSON><PERSON> would do
 */

const http = require('http');
const https = require('https');
const { URL } = require('url');

// Test configuration
const TEST_CONFIG = {
    timeout: 10000,
    retries: 3,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
};

// Services to test
const SERVICES = [
    {
        name: 'Main Website',
        baseUrl: 'http://51club.local',
        endpoints: [
            { path: '/', expectedStatus: 200, description: 'Homepage' },
            { path: '/index.html', expectedStatus: 200, description: 'Index page' },
            { path: '/assets/', expectedStatus: [200, 403, 404], description: 'Assets directory' },
            { path: '/images/', expectedStatus: [200, 403, 404], description: 'Images directory' },
            { path: '/nonexistent', expectedStatus: 404, description: 'Non-existent page' }
        ]
    },
    {
        name: 'API Service',
        baseUrl: 'http://api.51club.local',
        endpoints: [
            { path: '/', expectedStatus: 404, description: 'API Root (should return 404)' },
            { path: '/api/', expectedStatus: [200, 404], description: 'API directory' },
            { path: '/index.php', expectedStatus: 404, description: 'API index' },
            { path: '/conn.php', expectedStatus: [200, 404, 500], description: 'Database connection' },
            { path: '/functions2.php', expectedStatus: [200, 404, 500], description: 'API functions' }
        ]
    },
    {
        name: 'Games API',
        baseUrl: 'http://games.51club.local',
        endpoints: [
            { path: '/', expectedStatus: 404, description: 'Games API Root (Node.js)' },
            { path: '/health', expectedStatus: [200, 404], description: 'Health check endpoint' },
            { path: '/api/games', expectedStatus: [200, 404], description: 'Games endpoint' },
            { path: '/jili', expectedStatus: [200, 404], description: 'Jili games' },
            { path: '/jdb', expectedStatus: [200, 404], description: 'JDB games' }
        ]
    },
    {
        name: 'Admin Panel',
        baseUrl: 'http://admin.51club.local',
        endpoints: [
            { path: '/', expectedStatus: 200, description: 'Admin homepage' },
            { path: '/index.php', expectedStatus: 200, description: 'Admin index' },
            { path: '/login', expectedStatus: [200, 404], description: 'Admin login' },
            { path: '/dashboard.php', expectedStatus: [200, 302, 403], description: 'Admin dashboard' },
            { path: '/manage_user.php', expectedStatus: [200, 302, 403], description: 'User management' },
            { path: '/assets/', expectedStatus: [200, 403, 404], description: 'Admin assets' }
        ]
    },
    {
        name: 'Database Admin (phpMyAdmin)',
        baseUrl: 'http://db.51club.local',
        endpoints: [
            { path: '/', expectedStatus: 200, description: 'phpMyAdmin homepage' },
            { path: '/index.php', expectedStatus: 200, description: 'phpMyAdmin index' },
            { path: '/setup/', expectedStatus: [200, 403, 404], description: 'phpMyAdmin setup' },
            { path: '/themes/', expectedStatus: [200, 403, 404], description: 'phpMyAdmin themes' },
            { path: '/js/', expectedStatus: [200, 403, 404], description: 'phpMyAdmin JavaScript' }
        ]
    }
];

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// HTTP request function
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const isHttps = urlObj.protocol === 'https:';
        const client = isHttps ? https : http;
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || (isHttps ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'User-Agent': TEST_CONFIG.userAgent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                ...options.headers
            },
            timeout: TEST_CONFIG.timeout
        };

        const req = client.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    statusMessage: res.statusMessage,
                    headers: res.headers,
                    body: data,
                    url: url
                });
            });
        });

        req.on('error', reject);
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        if (options.body) {
            req.write(options.body);
        }
        
        req.end();
    });
}

// Test a single endpoint
async function testEndpoint(service, endpoint) {
    const url = service.baseUrl + endpoint.path;
    const expectedStatuses = Array.isArray(endpoint.expectedStatus) 
        ? endpoint.expectedStatus 
        : [endpoint.expectedStatus];

    try {
        const response = await makeRequest(url);
        const isExpected = expectedStatuses.includes(response.statusCode);
        
        const status = isExpected ? 'PASS' : 'FAIL';
        const color = isExpected ? colors.green : colors.red;
        
        console.log(`  ${color}${status}${colors.reset} ${endpoint.path.padEnd(20)} | HTTP ${response.statusCode} | ${endpoint.description}`);
        
        // Additional checks for specific content types
        if (response.statusCode === 200) {
            const contentType = response.headers['content-type'] || '';
            if (contentType.includes('text/html')) {
                const hasTitle = response.body.includes('<title>');
                const hasHtml = response.body.includes('<html');
                if (hasTitle || hasHtml) {
                    console.log(`    ${colors.cyan}✓${colors.reset} Valid HTML content detected`);
                }
            } else if (contentType.includes('application/json')) {
                try {
                    JSON.parse(response.body);
                    console.log(`    ${colors.cyan}✓${colors.reset} Valid JSON response`);
                } catch (e) {
                    console.log(`    ${colors.yellow}⚠${colors.reset} Invalid JSON response`);
                }
            }
        }

        return {
            url,
            status: response.statusCode,
            expected: isExpected,
            contentType: response.headers['content-type'],
            responseTime: Date.now() - startTime
        };
    } catch (error) {
        console.log(`  ${colors.red}ERROR${colors.reset} ${endpoint.path.padEnd(20)} | ${error.message} | ${endpoint.description}`);
        return {
            url,
            status: 'ERROR',
            expected: false,
            error: error.message
        };
    }
}

// Test a service
async function testService(service) {
    console.log(`\n${colors.bright}${colors.blue}🧪 Testing ${service.name}${colors.reset}`);
    console.log(`${colors.blue}Base URL: ${service.baseUrl}${colors.reset}`);
    console.log('─'.repeat(80));

    const results = [];
    let startTime = Date.now();

    for (const endpoint of service.endpoints) {
        startTime = Date.now();
        const result = await testEndpoint(service, endpoint);
        results.push(result);
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    const passed = results.filter(r => r.expected).length;
    const total = results.length;
    const passRate = ((passed / total) * 100).toFixed(1);

    console.log(`\n${colors.bright}Results: ${passed}/${total} tests passed (${passRate}%)${colors.reset}`);
    
    return {
        service: service.name,
        results,
        passed,
        total,
        passRate: parseFloat(passRate)
    };
}

// Main test runner
async function runAllTests() {
    console.log(`${colors.bright}${colors.magenta}🎰 51Club Casino - Playwright-style Endpoint Testing${colors.reset}`);
    console.log(`${colors.magenta}Test started at: ${new Date().toISOString()}${colors.reset}`);
    console.log('='.repeat(80));

    const allResults = [];
    let totalPassed = 0;
    let totalTests = 0;

    for (const service of SERVICES) {
        const serviceResult = await testService(service);
        allResults.push(serviceResult);
        totalPassed += serviceResult.passed;
        totalTests += serviceResult.total;
    }

    // Summary
    console.log(`\n${'='.repeat(80)}`);
    console.log(`${colors.bright}${colors.magenta}📊 FINAL TEST SUMMARY${colors.reset}`);
    console.log(`${'='.repeat(80)}`);

    allResults.forEach(result => {
        const color = result.passRate >= 80 ? colors.green : result.passRate >= 60 ? colors.yellow : colors.red;
        console.log(`${color}${result.service.padEnd(30)} | ${result.passed}/${result.total} (${result.passRate}%)${colors.reset}`);
    });

    const overallPassRate = ((totalPassed / totalTests) * 100).toFixed(1);
    const overallColor = overallPassRate >= 80 ? colors.green : overallPassRate >= 60 ? colors.yellow : colors.red;

    console.log(`\n${colors.bright}Overall Results: ${overallColor}${totalPassed}/${totalTests} tests passed (${overallPassRate}%)${colors.reset}`);
    
    if (overallPassRate >= 80) {
        console.log(`${colors.green}${colors.bright}🎉 All services are working well!${colors.reset}`);
    } else if (overallPassRate >= 60) {
        console.log(`${colors.yellow}${colors.bright}⚠️  Most services are working, some issues detected${colors.reset}`);
    } else {
        console.log(`${colors.red}${colors.bright}❌ Multiple service issues detected${colors.reset}`);
    }

    console.log(`\n${colors.cyan}Test completed at: ${new Date().toISOString()}${colors.reset}`);
}

// Run the tests
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runAllTests, testService, testEndpoint };
