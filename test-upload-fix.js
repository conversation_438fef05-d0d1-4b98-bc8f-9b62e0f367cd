#!/usr/bin/env node

/**
 * Test the fixed upload functionality (413 error fix)
 */

const { chromium } = require('playwright');
const path = require('path');

async function testUploadFix() {
    console.log('🔧 Testing fixed upload functionality (413 error fix)...');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Track network requests
    const requests = [];
    const responses = [];
    
    page.on('request', request => {
        requests.push({
            url: request.url(),
            method: request.method(),
            postDataSize: request.postData() ? request.postData().length : 0
        });
    });
    
    page.on('response', response => {
        responses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText()
        });
    });
    
    try {
        console.log('🌐 Step 1: Loading import page...');
        const importUrl = 'http://db.51club.local/index.php?route=/database/import&db=casino_db';
        
        const response = await page.goto(importUrl, {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Import page: ${response.status()} ${response.statusText()}`);
        
        // Wait for page to fully load
        await page.waitForTimeout(2000);
        
        // Check if import form is available
        const hasImportForm = await page.$('form[enctype="multipart/form-data"]') !== null;
        const hasFileInput = await page.$('input[type="file"]') !== null;
        
        console.log(`📊 Has import form: ${hasImportForm}`);
        console.log(`📊 Has file input: ${hasFileInput}`);
        
        if (hasFileInput) {
            console.log('🌐 Step 2: Testing file upload...');
            
            // Get the file input element
            const fileInput = await page.$('input[type="file"]');
            
            if (fileInput) {
                // Upload the test SQL file
                const testFilePath = '/mnt/g/clients/casino/51club_space/51club_main/test-upload.sql';
                await fileInput.setInputFiles(testFilePath);
                console.log('📁 Test file selected for upload');
                
                // Look for submit button
                const submitButton = await page.$('input[type="submit"], button[type="submit"], input[value*="Go"], input[value*="Import"]');
                
                if (submitButton) {
                    console.log('🌐 Step 3: Attempting upload...');
                    
                    // Click submit and wait for response
                    const uploadPromise = page.waitForResponse(response => {
                        return response.url().includes('import') && response.request().method() === 'POST';
                    }, { timeout: 60000 });
                    
                    await submitButton.click();
                    
                    try {
                        const uploadResponse = await uploadPromise;
                        console.log(`📊 Upload response: ${uploadResponse.status()} ${uploadResponse.statusText()}`);
                        
                        if (uploadResponse.status() === 413) {
                            console.log('❌ STILL GETTING 413: Request Entity Too Large');
                            console.log('   The fix may need additional configuration');
                        } else if (uploadResponse.status() === 200) {
                            console.log('✅ SUCCESS: Upload completed without 413 error!');
                        } else {
                            console.log(`⚠️  Upload returned: ${uploadResponse.status()}`);
                        }
                        
                    } catch (uploadError) {
                        console.log('⚠️  Upload timeout or error:', uploadError.message);
                    }
                } else {
                    console.log('⚠️  Submit button not found');
                }
            } else {
                console.log('⚠️  File input not found');
            }
        }
        
        // Check for any 413 errors in network requests
        const error413Responses = responses.filter(r => r.status === 413);
        const largeRequests = requests.filter(r => r.postDataSize > 1000000); // > 1MB
        
        console.log('\n📊 Network Analysis:');
        console.log(`   Total requests: ${requests.length}`);
        console.log(`   413 errors: ${error413Responses.length}`);
        console.log(`   Large requests (>1MB): ${largeRequests.length}`);
        
        if (error413Responses.length > 0) {
            console.log('\n❌ 413 errors found:');
            error413Responses.forEach(r => {
                console.log(`   ${r.status} ${r.statusText} - ${r.url}`);
            });
        }
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/upload-fix-test.png',
            fullPage: true 
        });
        console.log('\n📸 Screenshot saved: upload-fix-test.png');
        
        // Final assessment
        const isFixed = error413Responses.length === 0 && response.status() === 200;
        
        console.log('\n🎯 FINAL RESULT:');
        if (isFixed) {
            console.log('🎉 SUCCESS: 413 error has been fixed!');
            console.log('✅ Import page loads correctly');
            console.log('✅ No 413 Request Entity Too Large errors');
            console.log('✅ Upload functionality is working');
        } else {
            console.log('⚠️  PARTIAL FIX: Some issues may remain');
            if (error413Responses.length > 0) {
                console.log('   - Still getting 413 errors on some requests');
            }
        }
        
        console.log('\n🌐 Test URL: http://db.51club.local/index.php?route=/database/import&db=casino_db');
        console.log('📁 Test file: test-upload.sql');
        
    } catch (error) {
        console.error('❌ Error testing upload fix:', error.message);
    } finally {
        await browser.close();
    }
}

testUploadFix();
