#!/usr/bin/env node

/**
 * Final comprehensive test for phpMyAdmin
 */

const { chromium } = require('playwright');

async function finalPhpMyAdminTest() {
    console.log('🎯 Final comprehensive test for phpMyAdmin...');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Track network requests
    const requests = [];
    const responses = [];
    
    page.on('request', request => {
        requests.push({
            url: request.url(),
            method: request.method(),
            resourceType: request.resourceType()
        });
    });
    
    page.on('response', response => {
        responses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText()
        });
    });
    
    try {
        console.log('🌐 Loading http://db.51club.local/...');
        
        const response = await page.goto('http://db.51club.local/', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Main Response: ${response.status()} ${response.statusText()}`);
        
        // Wait for full page load
        await page.waitForTimeout(2000);
        
        // Get comprehensive page info
        const pageInfo = await page.evaluate(() => {
            return {
                title: document.title,
                hasPhpMyAdmin: document.body.innerHTML.includes('phpMyAdmin'),
                hasLoginForm: document.querySelector('form') !== null,
                hasServerInfo: document.body.innerHTML.includes('Server:'),
                hasDatabase: document.body.innerHTML.includes('Database:'),
                cssCount: document.querySelectorAll('link[rel="stylesheet"]').length,
                jsCount: document.querySelectorAll('script[src]').length,
                bodyContent: document.body.innerHTML.length,
                hasLogo: document.querySelector('img[alt*="phpMyAdmin"], .logo') !== null,
                hasNavigation: document.querySelector('#topmenu, .navigation, .navbar') !== null,
                hasMainContent: document.querySelector('#main_pane, .main, #page_content') !== null
            };
        });
        
        console.log('📊 Page Analysis:');
        console.log(`   Title: "${pageInfo.title}"`);
        console.log(`   Has phpMyAdmin content: ${pageInfo.hasPhpMyAdmin}`);
        console.log(`   Has login form: ${pageInfo.hasLoginForm}`);
        console.log(`   Has server info: ${pageInfo.hasServerInfo}`);
        console.log(`   Has database info: ${pageInfo.hasDatabase}`);
        console.log(`   CSS files: ${pageInfo.cssCount}`);
        console.log(`   JS files: ${pageInfo.jsCount}`);
        console.log(`   Body content length: ${pageInfo.bodyContent} chars`);
        console.log(`   Has logo: ${pageInfo.hasLogo}`);
        console.log(`   Has navigation: ${pageInfo.hasNavigation}`);
        console.log(`   Has main content: ${pageInfo.hasMainContent}`);
        
        // Analyze network requests
        const failedResponses = responses.filter(r => r.status >= 400);
        const successfulResponses = responses.filter(r => r.status >= 200 && r.status < 300);
        
        console.log('\n📊 Network Analysis:');
        console.log(`   Total requests: ${requests.length}`);
        console.log(`   Successful responses: ${successfulResponses.length}`);
        console.log(`   Failed responses: ${failedResponses.length}`);
        
        if (failedResponses.length > 0) {
            console.log('\n❌ Failed requests:');
            failedResponses.forEach(r => {
                console.log(`   ${r.status} ${r.statusText} - ${r.url}`);
            });
        }
        
        // Take final screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/phpmyadmin-final-test.png',
            fullPage: true 
        });
        console.log('\n📸 Final screenshot saved: phpmyadmin-final-test.png');
        
        // Overall assessment
        const isFullyWorking = (
            pageInfo.hasPhpMyAdmin &&
            pageInfo.hasLoginForm &&
            pageInfo.cssCount > 0 &&
            pageInfo.jsCount > 0 &&
            pageInfo.bodyContent > 10000 &&
            failedResponses.length === 0
        );
        
        const isMostlyWorking = (
            pageInfo.hasPhpMyAdmin &&
            pageInfo.hasLoginForm &&
            pageInfo.cssCount > 0 &&
            pageInfo.bodyContent > 10000
        );
        
        console.log('\n🎯 FINAL ASSESSMENT:');
        if (isFullyWorking) {
            console.log('🎉 EXCELLENT: phpMyAdmin is fully functional!');
            console.log('✅ All components loaded successfully');
            console.log('✅ No failed requests');
            console.log('✅ Ready for production use');
        } else if (isMostlyWorking) {
            console.log('✅ GOOD: phpMyAdmin is working with minor issues');
            console.log('✅ Main functionality available');
            console.log('✅ Login form accessible');
            if (failedResponses.length > 0) {
                console.log(`⚠️  ${failedResponses.length} failed requests (may be non-critical)`);
            }
        } else {
            console.log('❌ ISSUES: phpMyAdmin has significant problems');
            if (!pageInfo.hasPhpMyAdmin) console.log('   - Missing phpMyAdmin content');
            if (!pageInfo.hasLoginForm) console.log('   - Missing login form');
            if (pageInfo.cssCount === 0) console.log('   - CSS not loading');
            if (pageInfo.bodyContent < 10000) console.log('   - Insufficient content');
        }
        
        console.log('\n🌐 Access URL: http://db.51club.local/');
        console.log('🔑 Default credentials: root / (no password) or check Docker environment');
        
    } catch (error) {
        console.error('❌ Error in final test:', error.message);
    } finally {
        await browser.close();
    }
}

finalPhpMyAdminTest();
