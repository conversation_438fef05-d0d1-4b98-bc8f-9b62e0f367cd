#!/bin/bash

echo "🎰 51Club Casino - Complete Service Test"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test function
test_service() {
    local name="$1"
    local url="$2"
    local expected_status="$3"
    
    echo -n "Testing $name... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (HTTP $response)"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} (HTTP $response, expected $expected_status)"
        return 1
    fi
}

# Test with Host headers
test_service_with_host() {
    local name="$1"
    local host="$2"
    local expected_status="$3"
    
    echo -n "Testing $name with Host header... "
    
    response=$(curl -H "Host: $host" -s -o /dev/null -w "%{http_code}" http://localhost)
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (HTTP $response)"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} (HTTP $response, expected $expected_status)"
        return 1
    fi
}

echo "🔍 Testing Direct Backend Services:"
echo "-----------------------------------"

# Test backend services directly
test_service "Main Website (Direct)" "http://localhost:8080" "200" || true
test_service "API Service (Direct)" "http://localhost:8081" "404" || true  # 404 is expected for API root
test_service "Games API (Direct)" "http://localhost:3000" "404" || true   # 404 is expected for Games API root
test_service "Admin Panel (Direct)" "http://localhost:8082" "200" || true
test_service "phpMyAdmin (Direct)" "http://localhost:8083" "200" || true

echo ""
echo "🌐 Testing Clean URLs (through Nginx):"
echo "--------------------------------------"

# Test clean URLs through nginx
test_service_with_host "Main Website" "51club.local" "200"
test_service_with_host "API Service" "api.51club.local" "404"  # 404 is expected
test_service_with_host "Games API" "games.51club.local" "404"  # 404 is expected  
test_service_with_host "Admin Panel" "admin.51club.local" "200"
test_service_with_host "Database Admin" "db.51club.local" "200"

echo ""
echo "📊 Testing Service Content:"
echo "---------------------------"

# Test if services return expected content
echo -n "Main Website content... "
main_content=$(curl -H "Host: 51club.local" -s http://localhost | head -1)
if [[ "$main_content" == *"<!DOCTYPE html>"* ]] || [[ "$main_content" == *"<html"* ]]; then
    echo -e "${GREEN}✅ PASS${NC} (HTML content detected)"
else
    echo -e "${YELLOW}⚠️  WARN${NC} (Unexpected content: ${main_content:0:50}...)"
fi

echo -n "API Service JSON response... "
api_content=$(curl -H "Host: api.51club.local" -s http://localhost)
if [[ "$api_content" == *"json"* ]] || [[ "$api_content" == *"{"* ]]; then
    echo -e "${GREEN}✅ PASS${NC} (JSON response detected)"
else
    echo -e "${YELLOW}⚠️  WARN${NC} (Unexpected content)"
fi

echo -n "phpMyAdmin content... "
pma_content=$(curl -H "Host: db.51club.local" -s http://localhost | head -1)
if [[ "$pma_content" == *"<!DOCTYPE html>"* ]] || [[ "$pma_content" == *"phpMyAdmin"* ]]; then
    echo -e "${GREEN}✅ PASS${NC} (phpMyAdmin content detected)"
else
    echo -e "${YELLOW}⚠️  WARN${NC} (Unexpected content)"
fi

echo ""
echo "🔗 Clean URLs to test in browser:"
echo "--------------------------------"
echo -e "${BLUE}Main Website:${NC}     http://51club.local"
echo -e "${BLUE}API Service:${NC}      http://api.51club.local"
echo -e "${BLUE}Games API:${NC}        http://games.51club.local"
echo -e "${BLUE}Admin Panel:${NC}      http://admin.51club.local"
echo -e "${BLUE}Database Admin:${NC}   http://db.51club.local"

echo ""
echo "📝 Notes:"
echo "--------"
echo "• API and Games API returning 404 is normal (they need specific endpoints)"
echo "• Make sure to add domains to your hosts file using update-hosts-clean.bat"
echo "• All services are running on standard ports 80/443 (no port numbers needed)"

echo ""
echo "✅ Service test completed!"
