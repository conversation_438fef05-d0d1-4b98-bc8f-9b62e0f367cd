#!/usr/bin/env node

/**
 * 51Club Casino - Playwright MCP Integration Test Suite
 * This script provides MCP-compatible testing for all casino services
 */

const { chromium } = require('playwright');
const http = require('http');

// MCP-style configuration
const MCP_CONFIG = {
    timeout: 30000,
    headless: true,
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
};

// Services to test with MCP-style definitions
const SERVICES = [
    {
        name: 'Main Website',
        url: 'http://51club.local',
        tests: [
            { action: 'goto', selector: null, expected: 'title', description: 'Load homepage' },
            { action: 'screenshot', selector: null, expected: null, description: 'Capture homepage' },
            { action: 'check_element', selector: 'body', expected: 'exists', description: 'Verify page structure' },
            { action: 'check_text', selector: 'title', expected: 'contains', description: 'Check page title' }
        ]
    },
    {
        name: 'API Service',
        url: 'http://api.51club.local',
        tests: [
            { action: 'goto', selector: null, expected: '404', description: 'API root should return 404' },
            { action: 'check_response', selector: null, expected: 'json', description: 'Verify JSON response' }
        ]
    },
    {
        name: 'Games API',
        url: 'http://games.51club.local',
        tests: [
            { action: 'goto', selector: null, expected: '404', description: 'Games API root' },
            { action: 'check_response', selector: null, expected: 'node_error', description: 'Verify Node.js error page' }
        ]
    },
    {
        name: 'Admin Panel',
        url: 'http://admin.51club.local',
        tests: [
            { action: 'goto', selector: null, expected: 'title', description: 'Load admin panel' },
            { action: 'screenshot', selector: null, expected: null, description: 'Capture admin interface' },
            { action: 'check_element', selector: 'form', expected: 'exists', description: 'Check for login form' }
        ]
    },
    {
        name: 'Database Admin',
        url: 'http://db.51club.local',
        tests: [
            { action: 'goto', selector: null, expected: 'title', description: 'Load phpMyAdmin' },
            { action: 'screenshot', selector: null, expected: null, description: 'Capture phpMyAdmin' },
            { action: 'check_text', selector: 'title', expected: 'phpMyAdmin', description: 'Verify phpMyAdmin title' }
        ]
    }
];

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// MCP-style action handlers
class PlaywrightMCP {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.results = [];
    }

    async initialize() {
        console.log(`${colors.cyan}🚀 Initializing Playwright MCP Browser...${colors.reset}`);
        this.browser = await chromium.launch({
            headless: MCP_CONFIG.headless,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        this.context = await this.browser.newContext({
            viewport: MCP_CONFIG.viewport,
            userAgent: MCP_CONFIG.userAgent
        });
        
        this.page = await this.context.newPage();
        
        // Set timeout
        this.page.setDefaultTimeout(MCP_CONFIG.timeout);
        
        console.log(`${colors.green}✅ Browser initialized successfully${colors.reset}`);
    }

    async goto(url) {
        console.log(`${colors.blue}🌐 Navigating to: ${url}${colors.reset}`);
        try {
            const response = await this.page.goto(url, { 
                waitUntil: 'domcontentloaded',
                timeout: MCP_CONFIG.timeout 
            });
            
            const status = response.status();
            const statusText = response.statusText();
            
            console.log(`${colors.cyan}   Response: HTTP ${status} ${statusText}${colors.reset}`);
            
            return {
                success: true,
                status,
                statusText,
                url: response.url()
            };
        } catch (error) {
            console.log(`${colors.red}   Error: ${error.message}${colors.reset}`);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async screenshot(name) {
        try {
            const filename = `screenshot-${name.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}.png`;
            const path = `/mnt/g/clients/casino/51club_space/51club_main/screenshots/${filename}`;
            
            // Create screenshots directory if it doesn't exist
            await this.page.evaluate(() => {
                // Ensure page is fully loaded
                return document.readyState === 'complete';
            });
            
            await this.page.screenshot({ 
                path, 
                fullPage: true,
                type: 'png'
            });
            
            console.log(`${colors.green}📸 Screenshot saved: ${filename}${colors.reset}`);
            return { success: true, path, filename };
        } catch (error) {
            console.log(`${colors.red}📸 Screenshot failed: ${error.message}${colors.reset}`);
            return { success: false, error: error.message };
        }
    }

    async checkElement(selector) {
        try {
            const element = await this.page.$(selector);
            const exists = element !== null;
            
            if (exists) {
                const tagName = await element.evaluate(el => el.tagName);
                console.log(`${colors.green}✅ Element found: ${selector} (${tagName})${colors.reset}`);
            } else {
                console.log(`${colors.yellow}⚠️  Element not found: ${selector}${colors.reset}`);
            }
            
            return { success: true, exists, selector };
        } catch (error) {
            console.log(`${colors.red}❌ Element check failed: ${error.message}${colors.reset}`);
            return { success: false, error: error.message };
        }
    }

    async checkText(selector, expectedText) {
        try {
            const element = await this.page.$(selector);
            if (!element) {
                return { success: false, error: `Element ${selector} not found` };
            }
            
            const text = await element.textContent();
            const contains = text && text.toLowerCase().includes(expectedText.toLowerCase());
            
            if (contains) {
                console.log(`${colors.green}✅ Text check passed: "${expectedText}" found in ${selector}${colors.reset}`);
            } else {
                console.log(`${colors.yellow}⚠️  Text check failed: "${expectedText}" not found in ${selector}${colors.reset}`);
                console.log(`${colors.cyan}   Actual text: "${text}"${colors.reset}`);
            }
            
            return { success: true, contains, actualText: text, expectedText };
        } catch (error) {
            console.log(`${colors.red}❌ Text check failed: ${error.message}${colors.reset}`);
            return { success: false, error: error.message };
        }
    }

    async getPageInfo() {
        try {
            const title = await this.page.title();
            const url = this.page.url();
            const content = await this.page.content();
            
            return {
                title,
                url,
                contentLength: content.length,
                hasHtml: content.includes('<html'),
                hasBody: content.includes('<body'),
                hasTitle: content.includes('<title>')
            };
        } catch (error) {
            return { error: error.message };
        }
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
            console.log(`${colors.cyan}🔒 Browser closed${colors.reset}`);
        }
    }
}

// Test runner
async function runMCPTests() {
    console.log(`${colors.bright}${colors.magenta}🎰 51Club Casino - Playwright MCP Test Suite${colors.reset}`);
    console.log(`${colors.magenta}Test started at: ${new Date().toISOString()}${colors.reset}`);
    console.log('='.repeat(80));

    // Create screenshots directory
    await require('fs').promises.mkdir('/mnt/g/clients/casino/51club_space/51club_main/screenshots', { recursive: true }).catch(() => {});

    const mcp = new PlaywrightMCP();
    const allResults = [];

    try {
        await mcp.initialize();

        for (const service of SERVICES) {
            console.log(`\n${colors.bright}${colors.blue}🧪 Testing ${service.name}${colors.reset}`);
            console.log(`${colors.blue}URL: ${service.url}${colors.reset}`);
            console.log('─'.repeat(60));

            const serviceResults = {
                name: service.name,
                url: service.url,
                tests: [],
                passed: 0,
                total: 0
            };

            // Navigate to service
            const gotoResult = await mcp.goto(service.url);
            serviceResults.gotoResult = gotoResult;

            if (gotoResult.success) {
                // Get page info
                const pageInfo = await mcp.getPageInfo();
                console.log(`${colors.cyan}📄 Page Title: "${pageInfo.title}"${colors.reset}`);
                console.log(`${colors.cyan}📄 Content Length: ${pageInfo.contentLength} bytes${colors.reset}`);

                // Take screenshot
                await mcp.screenshot(service.name);

                // Run service-specific tests
                for (const test of service.tests) {
                    serviceResults.total++;
                    console.log(`\n${colors.yellow}🔍 ${test.description}${colors.reset}`);

                    let testResult = { description: test.description, success: false };

                    switch (test.action) {
                        case 'check_element':
                            testResult = await mcp.checkElement(test.selector);
                            break;
                        case 'check_text':
                            testResult = await mcp.checkText(test.selector, test.expected);
                            break;
                        case 'check_response':
                            // Already handled in goto
                            testResult = { success: true, note: 'Response checked in navigation' };
                            break;
                        default:
                            testResult = { success: true, note: 'Test completed' };
                    }

                    if (testResult.success) {
                        serviceResults.passed++;
                    }

                    serviceResults.tests.push(testResult);
                }
            } else {
                console.log(`${colors.red}❌ Failed to load ${service.name}${colors.reset}`);
            }

            const passRate = serviceResults.total > 0 ? ((serviceResults.passed / serviceResults.total) * 100).toFixed(1) : 0;
            console.log(`\n${colors.bright}Results: ${serviceResults.passed}/${serviceResults.total} tests passed (${passRate}%)${colors.reset}`);

            allResults.push(serviceResults);

            // Small delay between services
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

    } catch (error) {
        console.error(`${colors.red}❌ Test suite error: ${error.message}${colors.reset}`);
    } finally {
        await mcp.close();
    }

    // Final summary
    console.log(`\n${'='.repeat(80)}`);
    console.log(`${colors.bright}${colors.magenta}📊 FINAL MCP TEST SUMMARY${colors.reset}`);
    console.log(`${'='.repeat(80)}`);

    let totalPassed = 0;
    let totalTests = 0;

    allResults.forEach(result => {
        totalPassed += result.passed;
        totalTests += result.total;
        const passRate = result.total > 0 ? ((result.passed / result.total) * 100).toFixed(1) : 0;
        const color = passRate >= 80 ? colors.green : passRate >= 60 ? colors.yellow : colors.red;
        console.log(`${color}${result.name.padEnd(30)} | ${result.passed}/${result.total} (${passRate}%)${colors.reset}`);
    });

    const overallPassRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0;
    const overallColor = overallPassRate >= 80 ? colors.green : overallPassRate >= 60 ? colors.yellow : colors.red;

    console.log(`\n${colors.bright}Overall MCP Results: ${overallColor}${totalPassed}/${totalTests} tests passed (${overallPassRate}%)${colors.reset}`);

    console.log(`\n${colors.cyan}📸 Screenshots saved in: /mnt/g/clients/casino/51club_space/51club_main/screenshots/${colors.reset}`);
    console.log(`${colors.cyan}Test completed at: ${new Date().toISOString()}${colors.reset}`);

    return {
        totalPassed,
        totalTests,
        overallPassRate: parseFloat(overallPassRate),
        services: allResults
    };
}

// Check if Playwright is installed
async function checkPlaywrightInstallation() {
    try {
        require('playwright');
        return true;
    } catch (error) {
        console.log(`${colors.red}❌ Playwright not installed. Installing...${colors.reset}`);
        return false;
    }
}

// Main execution
if (require.main === module) {
    checkPlaywrightInstallation().then(async (installed) => {
        if (!installed) {
            console.log(`${colors.yellow}Please install Playwright first:${colors.reset}`);
            console.log(`${colors.cyan}npm install playwright${colors.reset}`);
            console.log(`${colors.cyan}npx playwright install${colors.reset}`);
            return;
        }
        
        await runMCPTests();
    }).catch(console.error);
}

module.exports = { PlaywrightMCP, runMCPTests };
