@echo off
echo Adding 51Club domains to hosts file for clean URLs...

echo. >> C:\Windows\System32\drivers\etc\hosts
echo # 51Club Casino Local Development - Clean URLs >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1    51club.local >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1    www.51club.local >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1    api.51club.local >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1    games.51club.local >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1    admin.51club.local >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1    db.51club.local >> C:\Windows\System32\drivers\etc\hosts

echo Hosts file updated!
echo Flushing DNS cache...
ipconfig /flushdns

echo Done! You can now access with clean URLs:
echo - http://51club.local
echo - http://api.51club.local
echo - http://games.51club.local
echo - http://admin.51club.local
echo - http://db.51club.local

pause
