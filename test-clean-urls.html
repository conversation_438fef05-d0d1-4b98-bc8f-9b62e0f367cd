<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>51Club Casino - Clean URLs Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .service-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
        .service-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .service-card h3 { margin-top: 0; color: #007bff; }
        .service-card a { color: #007bff; text-decoration: none; font-weight: bold; display: block; margin: 10px 0; }
        .service-card a:hover { text-decoration: underline; }
        .status { padding: 5px 10px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status.checking { background: #fff3cd; color: #856404; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .test-button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 10px 0; }
        .test-button:hover { background: #0056b3; }
        .results { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎰 51Club Casino - Clean URLs Test</h1>
        <p>Testing all services with clean URLs (no port numbers):</p>

        <div class="service-grid">
            <div class="service-card">
                <h3>🏠 Main Website</h3>
                <p>Primary casino website</p>
                <a href="http://51club.local" target="_blank">http://51club.local</a>
                <div class="status checking" id="main-status">Checking...</div>
                <button class="test-button" onclick="testService('http://51club.local', 'main-status')">Test Service</button>
            </div>

            <div class="service-card">
                <h3>🔌 API Service</h3>
                <p>Backend API endpoints</p>
                <a href="http://api.51club.local" target="_blank">http://api.51club.local</a>
                <div class="status checking" id="api-status">Checking...</div>
                <button class="test-button" onclick="testService('http://api.51club.local', 'api-status')">Test Service</button>
            </div>

            <div class="service-card">
                <h3>🎮 Games API</h3>
                <p>Node.js game integration</p>
                <a href="http://games.51club.local" target="_blank">http://games.51club.local</a>
                <div class="status checking" id="games-status">Checking...</div>
                <button class="test-button" onclick="testService('http://games.51club.local', 'games-status')">Test Service</button>
            </div>

            <div class="service-card">
                <h3>⚙️ Admin Panel</h3>
                <p>Administrative interface</p>
                <a href="http://admin.51club.local" target="_blank">http://admin.51club.local</a>
                <div class="status checking" id="admin-status">Checking...</div>
                <button class="test-button" onclick="testService('http://admin.51club.local', 'admin-status')">Test Service</button>
            </div>

            <div class="service-card">
                <h3>🗄️ Database Admin</h3>
                <p>phpMyAdmin interface</p>
                <a href="http://db.51club.local" target="_blank">http://db.51club.local</a>
                <div class="status checking" id="db-status">Checking...</div>
                <button class="test-button" onclick="testService('http://db.51club.local', 'db-status')">Test Service</button>
            </div>
        </div>

        <div class="results" id="results">
            <h3>Test Results:</h3>
            <p>Click "Test Service" buttons above to check each service status.</p>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 5px;">
            <h3>Setup Instructions:</h3>
            <ol>
                <li><strong>Windows Hosts File:</strong> Run <code>update-hosts-clean.bat</code> as Administrator</li>
                <li><strong>Docker Services:</strong> All containers should be running on ports 80/443</li>
                <li><strong>DNS Cache:</strong> Run <code>ipconfig /flushdns</code> if needed</li>
            </ol>
        </div>
    </div>

    <script>
        async function testService(url, statusId) {
            const statusElement = document.getElementById(statusId);
            const resultsElement = document.getElementById('results');
            
            statusElement.textContent = 'Testing...';
            statusElement.className = 'status checking';
            
            try {
                const response = await fetch(url, { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                statusElement.textContent = 'Online';
                statusElement.className = 'status online';
                
                resultsElement.innerHTML += `<p>✅ ${url} - Service is accessible</p>`;
                
            } catch (error) {
                statusElement.textContent = 'Offline';
                statusElement.className = 'status offline';
                
                resultsElement.innerHTML += `<p>❌ ${url} - Service not accessible: ${error.message}</p>`;
            }
        }

        // Auto-test all services on page load
        window.onload = function() {
            setTimeout(() => {
                testService('http://51club.local', 'main-status');
                testService('http://api.51club.local', 'api-status');
                testService('http://games.51club.local', 'games-status');
                testService('http://admin.51club.local', 'admin-status');
                testService('http://db.51club.local', 'db-status');
            }, 1000);
        };
    </script>
</body>
</html>
