<VirtualHost *:80>
    ServerName localhost
    DocumentRoot /var/www/html
    
    # Enable rewrite engine
    RewriteEngine On
    
    # Handle SSL forwarded headers
    SetEnvIf X-Forwarded-Proto "https" HTTPS=on
    SetEnvIf X-Forwarded-Port "443" SERVER_PORT=443
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # SSL-aware redirects (disabled for development)
    # RewriteCond %{HTTP:X-Forwarded-Proto} !https
    # RewriteCond %{REQUEST_URI} !^/health
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Directory settings
    <Directory /var/www/html>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # PHP settings
        php_admin_value upload_max_filesize 100M
        php_admin_value post_max_size 100M
        php_admin_value memory_limit 256M
        php_admin_value max_execution_time 300
        
        # SSL-aware PHP settings
        php_admin_value auto_prepend_file "/var/www/html/.ssl-config.php"
    </Directory>
    
    # Deny access to sensitive files
    <FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
        Require all denied
    </FilesMatch>
    
    # Allow .ssl-config.php
    <Files ".ssl-config.php">
        Require all granted
    </Files>
    
    # Compress static files
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/json
    </IfModule>
    
    # Cache static files
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/pdf "access plus 1 month"
        ExpiresByType text/javascript "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType application/x-javascript "access plus 1 month"
        ExpiresByType application/x-shockwave-flash "access plus 1 month"
        ExpiresByType image/x-icon "access plus 1 year"
        ExpiresDefault "access plus 2 days"
    </IfModule>
    
    # Logging
    ErrorLog /var/log/apache2/error.log
    CustomLog /var/log/apache2/access.log combined
    
    # SSL-aware logging
    LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" SSL:%{X-Forwarded-Proto}i" ssl_combined
    CustomLog /var/log/apache2/ssl_access.log ssl_combined
</VirtualHost>
