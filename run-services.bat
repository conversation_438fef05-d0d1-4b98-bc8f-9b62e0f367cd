@echo off
echo 🎰 51Club Casino - Complete Service Startup
echo ==========================================
echo.

REM Check if Docker is running
docker info >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Docker is running
) else (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo.
echo 🏗️  Building all services...
echo ==============================
docker-compose build --no-cache

if %errorLevel% neq 0 (
    echo ❌ Build failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo 🚀 Starting all services...
echo ============================
docker-compose up -d

if %errorLevel% neq 0 (
    echo ❌ Failed to start services. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ⏳ Waiting for services to initialize...
timeout /t 20 /nobreak >nul

echo.
echo 📊 Service Status:
echo ==================
docker-compose ps

echo.
echo 🔐 Checking SSL certificates...
echo ===============================
docker exec 51club_nginx test -f /etc/nginx/ssl/51club.local.crt >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ SSL certificates are ready
) else (
    echo ⚠️  Generating SSL certificates...
    docker exec 51club_nginx /usr/local/bin/generate-ssl.sh
)

echo.
echo 🌐 Your 51Club Casino is now running!
echo =====================================
echo.
echo 🔗 Access your services:
echo   🏠 Main Site:    https://51club.local
echo   🔌 API:          https://api.51club.local
echo   🎮 Games API:    https://games.51club.local
echo   ⚙️  Admin Panel:  https://admin.51club.local
echo   🗄️  phpMyAdmin:  https://db.51club.local
echo.
echo 🔐 SSL Security Features:
echo   ✅ All endpoints secured with SSL/TLS
echo   ✅ HTTP to HTTPS redirects
echo   ✅ Security headers configured
echo   ✅ Rate limiting enabled
echo.
echo 💡 To install SSL certificates (remove browser warnings):
echo   Run 'install-ssl-windows.bat' as Administrator
echo.
echo 📝 Useful commands:
echo   📋 View logs:        docker-compose logs -f
echo   🛑 Stop services:    docker-compose down
echo   🔄 Restart service:  docker-compose restart [service_name]
echo   📊 Service status:   docker-compose ps
echo.
echo 🎉 Setup complete! Happy gaming! 🎲
echo.
pause
