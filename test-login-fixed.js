#!/usr/bin/env node

/**
 * Fixed login test with proper selectors
 */

const { chromium } = require('playwright');

async function testLoginFixed() {
    console.log('🔐 Testing Login - Fixed Version');
    console.log('URL: http://51club.local/#/login');
    console.log('Username: 3124003124');
    console.log('Password: 3124003124');
    console.log('='.repeat(60));
    
    const browser = await chromium.launch({ 
        headless: false,
        devtools: true
    });
    
    const context = await browser.newContext({
        ignoreHTTPSErrors: true
    });
    
    const page = await context.newPage();
    
    // Track errors and API calls
    const errors = [];
    const apiCalls = [];
    const loginResponses = [];
    
    // Console monitoring
    page.on('console', msg => {
        if (msg.type() === 'error') {
            errors.push(msg.text());
            console.log(`❌ [ERROR] ${msg.text()}`);
        }
    });
    
    // Network monitoring for login-related requests
    page.on('request', request => {
        if (request.url().includes('api') && 
            (request.url().includes('login') || request.url().includes('auth') || 
             request.url().includes('signin') || request.postData()?.includes('3124003124'))) {
            console.log(`🔑 [LOGIN REQUEST] ${request.method()} ${request.url()}`);
            apiCalls.push({
                url: request.url(),
                method: request.method(),
                postData: request.postData()
            });
        }
    });
    
    page.on('response', response => {
        if (response.url().includes('api') && 
            (response.url().includes('login') || response.url().includes('auth') || 
             response.url().includes('signin'))) {
            console.log(`🔑 [LOGIN RESPONSE] ${response.status()} ${response.url()}`);
            loginResponses.push({
                url: response.url(),
                status: response.status(),
                statusText: response.statusText()
            });
        }
    });
    
    try {
        console.log('🌐 Step 1: Loading login page...');
        
        await page.goto('http://51club.local/#/login', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log('✅ Login page loaded');
        await page.waitForTimeout(3000);
        
        // Take initial screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/login-test-1.png',
            fullPage: true 
        });
        
        console.log('\n🔍 Step 2: Finding login form elements...');
        
        // Find form elements with more specific approach
        const formElements = await page.evaluate(() => {
            const inputs = Array.from(document.querySelectorAll('input'));
            const buttons = Array.from(document.querySelectorAll('button'));
            
            const textInputs = inputs.filter(input => 
                input.type === 'text' || input.type === 'tel' || input.type === 'email' || 
                input.name?.includes('user') || input.placeholder?.includes('user') ||
                input.name?.includes('phone') || input.placeholder?.includes('phone')
            );
            
            const passwordInputs = inputs.filter(input => input.type === 'password');
            
            const submitButtons = buttons.filter(button => 
                button.type === 'submit' || 
                button.textContent?.includes('Login') || 
                button.textContent?.includes('Sign') ||
                button.className?.includes('login') ||
                button.className?.includes('submit')
            );
            
            return {
                textInputsCount: textInputs.length,
                passwordInputsCount: passwordInputs.length,
                submitButtonsCount: submitButtons.length,
                allInputsCount: inputs.length,
                allButtonsCount: buttons.length
            };
        });
        
        console.log(`   Text inputs found: ${formElements.textInputsCount}`);
        console.log(`   Password inputs found: ${formElements.passwordInputsCount}`);
        console.log(`   Submit buttons found: ${formElements.submitButtonsCount}`);
        console.log(`   Total inputs: ${formElements.allInputsCount}`);
        console.log(`   Total buttons: ${formElements.allButtonsCount}`);
        
        if (formElements.textInputsCount > 0 && formElements.passwordInputsCount > 0) {
            console.log('\n🔑 Step 3: Attempting login...');
            
            // Find and fill username field
            const usernameField = await page.$('input[type="text"], input[type="tel"], input[type="email"]');
            if (usernameField) {
                await usernameField.click();
                await usernameField.fill('3124003124');
                console.log('   ✅ Username entered: 3124003124');
            }
            
            // Find and fill password field
            const passwordField = await page.$('input[type="password"]');
            if (passwordField) {
                await passwordField.click();
                await passwordField.fill('3124003124');
                console.log('   ✅ Password entered: 3124003124');
            }
            
            // Take screenshot with filled form
            await page.screenshot({ 
                path: '/mnt/g/clients/casino/51club_space/51club_main/login-test-2-filled.png',
                fullPage: true 
            });
            
            // Find and click submit button
            const submitButton = await page.$('button[type="submit"]') || 
                                await page.$('button:has-text("Login")') ||
                                await page.$('button:has-text("登录")') ||
                                await page.$('button:has-text("Sign")') ||
                                await page.$('.login-btn') ||
                                await page.$('form button');
            
            if (submitButton) {
                console.log('   🔄 Clicking submit button...');
                
                // Set up response monitoring
                const responsePromise = page.waitForResponse(response => {
                    return response.url().includes('api') && response.request().method() === 'POST';
                }, { timeout: 15000 }).catch(() => null);
                
                await submitButton.click();
                
                // Wait for response
                const response = await responsePromise;
                if (response) {
                    console.log(`   📊 Login API response: ${response.status()} ${response.statusText()}`);
                    
                    try {
                        const responseText = await response.text();
                        console.log(`   📄 Response: ${responseText.substring(0, 300)}...`);
                        
                        // Parse JSON response if possible
                        try {
                            const responseJson = JSON.parse(responseText);
                            if (responseJson.code !== undefined) {
                                console.log(`   📊 Response code: ${responseJson.code}`);
                                console.log(`   📊 Response message: ${responseJson.msg || responseJson.message || 'No message'}`);
                                
                                if (responseJson.code === 0 || responseJson.code === 200) {
                                    console.log('   ✅ Login appears successful based on response code');
                                } else {
                                    console.log('   ❌ Login failed based on response code');
                                }
                            }
                        } catch (e) {
                            console.log('   📄 Response is not JSON format');
                        }
                    } catch (e) {
                        console.log('   Could not read response body');
                    }
                }
                
                // Wait for potential page change
                await page.waitForTimeout(5000);
                
                // Check current URL
                const currentUrl = page.url();
                console.log(`   📍 Current URL: ${currentUrl}`);
                
                // Check for success indicators
                const isStillOnLogin = currentUrl.includes('login');
                console.log(`   📊 Still on login page: ${isStillOnLogin}`);
                
                if (!isStillOnLogin) {
                    console.log('   ✅ Login successful - redirected away from login page');
                } else {
                    console.log('   ⚠️ Still on login page - checking for error messages');
                    
                    // Look for error messages in the page
                    const errorMessages = await page.evaluate(() => {
                        const possibleErrorElements = document.querySelectorAll(
                            '.error, .alert, .warning, .message, [class*="error"], [class*="alert"]'
                        );
                        
                        const messages = [];
                        possibleErrorElements.forEach(el => {
                            if (el.textContent && el.textContent.trim()) {
                                messages.push(el.textContent.trim());
                            }
                        });
                        
                        return messages;
                    });
                    
                    if (errorMessages.length > 0) {
                        console.log('   ❌ Error messages found:');
                        errorMessages.forEach((msg, i) => {
                            console.log(`      ${i + 1}. ${msg}`);
                        });
                    } else {
                        console.log('   ℹ️ No visible error messages found');
                    }
                }
                
            } else {
                console.log('   ❌ Could not find submit button');
            }
            
        } else {
            console.log('   ❌ Login form elements not found properly');
        }
        
        // Take final screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/login-test-3-final.png',
            fullPage: true 
        });
        
        // Final analysis
        console.log('\n' + '='.repeat(60));
        console.log('📊 LOGIN TEST RESULTS');
        console.log('='.repeat(60));
        
        console.log(`🔑 Login API calls made: ${apiCalls.length}`);
        console.log(`📊 Login responses received: ${loginResponses.length}`);
        console.log(`❌ Console errors: ${errors.length}`);
        
        if (loginResponses.length > 0) {
            console.log('\n📊 LOGIN API RESPONSES:');
            loginResponses.forEach((resp, i) => {
                console.log(`   ${i + 1}. ${resp.status} ${resp.statusText} - ${resp.url}`);
            });
        }
        
        if (errors.length > 0) {
            console.log('\n❌ CONSOLE ERRORS:');
            errors.forEach((error, i) => {
                console.log(`   ${i + 1}. ${error}`);
            });
        }
        
        console.log('\n🎯 SUMMARY:');
        if (loginResponses.length > 0) {
            console.log('✅ Login request was sent to API');
            console.log('   Check the API response details above');
        } else {
            console.log('⚠️ No login API requests detected');
            console.log('   The login form may not be configured properly');
        }
        
        console.log('\n📸 Screenshots saved:');
        console.log('   1. login-test-1.png - Initial login page');
        console.log('   2. login-test-2-filled.png - Form filled with credentials');
        console.log('   3. login-test-3-final.png - Final result');
        
    } catch (error) {
        console.error('❌ Test error:', error.message);
    } finally {
        await browser.close();
    }
}

testLoginFixed();
