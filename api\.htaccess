RewriteEngine On
SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1
#RewriteCond %{REQUEST_URI} !/Under-Maintainance.jpg$ [NC]
#RewriteRule ^(.*)$ /Under-Maintainance.jpg [R=302,L]
# HTTPS redirect disabled for development
# RewriteCond %{HTTPS}  !=on
# RewriteRule ^/?(.*) https://%{SERVER_NAME}/$1 [R,L]
IndexIgnore *
ErrorDocument 404 /index.php

# HTTPS redirect in other rules disabled for development
# RewriteCond %{HTTP_HOST} !^www\. [NC]
# RewriteCond %{REQUEST_URI} ^(.*/)index\.php$ [NC]
# RewriteRule . https://%{HTTP_HOST}%1 [R=301,NE,L]

## hide .php extension
# To externally redirect /dir/foo.php to /dir/foo
RewriteCond %{REQUEST_METHOD} !POST [NC]
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s([^.]+)\.php [NC]
RewriteRule ^ %1 [R,L,NC]

## To internally redirect /dir/foo to /dir/foo.php
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^ %{REQUEST_URI}.php [L]

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php81” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php81 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
