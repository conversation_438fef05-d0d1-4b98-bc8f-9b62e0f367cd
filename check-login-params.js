#!/usr/bin/env node

/**
 * Check what parameters the login API expects
 */

const { chromium } = require('playwright');

async function checkLoginParams() {
    console.log('🔍 Checking Login API Parameters');
    console.log('='.repeat(40));
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Intercept and log all requests
    page.on('request', request => {
        if (request.url().includes('Login')) {
            console.log(`🔑 LOGIN REQUEST:`);
            console.log(`   URL: ${request.url()}`);
            console.log(`   Method: ${request.method()}`);
            console.log(`   Headers:`, request.headers());
            console.log(`   Post Data: ${request.postData()}`);
        }
    });
    
    page.on('response', response => {
        if (response.url().includes('Login')) {
            console.log(`📊 LOGIN RESPONSE:`);
            console.log(`   Status: ${response.status()}`);
            response.text().then(text => {
                console.log(`   Body: ${text}`);
            });
        }
    });
    
    try {
        await page.goto('http://51club.local/#/login');
        await page.waitForTimeout(3000);
        
        // Fill form
        await page.fill('input[type="text"]', '3124003124');
        await page.fill('input[type="password"]', '3124003124');
        
        // Click login button
        const loginButton = await page.$('button:has-text("Log in")') || 
                           await page.$('button[type="submit"]') ||
                           await page.$('button:first-of-type');
        
        if (loginButton) {
            await loginButton.click();
            await page.waitForTimeout(5000);
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await browser.close();
    }
}

checkLoginParams();
