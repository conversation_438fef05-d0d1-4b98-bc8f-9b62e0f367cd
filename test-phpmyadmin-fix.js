#!/usr/bin/env node

/**
 * Test phpMyAdmin fix - Check if blank screen issue is resolved
 */

const { chromium } = require('playwright');

async function testPhpMyAdminFix() {
    console.log('🔧 Testing phpMyAdmin fix for blank screen issue...');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        console.log('🌐 Navigating to http://db.51club.local/...');
        
        // Clear any cached data
        await context.clearCookies();
        
        const response = await page.goto('http://db.51club.local/', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Response Status: ${response.status()}`);
        console.log(`📊 Response Status Text: ${response.statusText()}`);
        
        // Wait for page to fully load
        await page.waitForTimeout(3000);
        
        // Get page content and title
        const content = await page.content();
        const title = await page.title();
        
        console.log(`📊 Page Title: "${title}"`);
        console.log(`📊 Content Length: ${content.length} bytes`);
        
        // Check for phpMyAdmin specific elements
        const hasPhpMyAdminTitle = title.includes('phpMyAdmin');
        const hasPhpMyAdminContent = content.includes('phpMyAdmin');
        const hasLoginForm = await page.$('form') !== null;
        const hasServerSelection = content.includes('Server:') || content.includes('server');
        
        console.log(`📊 Has phpMyAdmin title: ${hasPhpMyAdminTitle}`);
        console.log(`📊 Has phpMyAdmin content: ${hasPhpMyAdminContent}`);
        console.log(`📊 Has login form: ${hasLoginForm}`);
        console.log(`📊 Has server selection: ${hasServerSelection}`);
        
        // Check for CSS and JS loading
        const cssLinks = await page.$$('link[rel="stylesheet"]');
        const scriptTags = await page.$$('script[src]');
        
        console.log(`📊 CSS files loaded: ${cssLinks.length}`);
        console.log(`📊 JS files loaded: ${scriptTags.length}`);
        
        // Check for specific phpMyAdmin elements
        const logoExists = await page.$('img[alt*="phpMyAdmin"], .logo') !== null;
        const navigationExists = await page.$('#topmenu, .navigation') !== null;
        
        console.log(`📊 Logo exists: ${logoExists}`);
        console.log(`📊 Navigation exists: ${navigationExists}`);
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/phpmyadmin-fixed-screenshot.png',
            fullPage: true 
        });
        console.log('📸 Screenshot saved: phpmyadmin-fixed-screenshot.png');
        
        // Test result
        const isWorking = hasPhpMyAdminContent && (hasLoginForm || hasServerSelection) && cssLinks.length > 0;
        
        if (isWorking) {
            console.log('✅ SUCCESS: phpMyAdmin is now working properly!');
            console.log('✅ The blank screen issue has been fixed!');
        } else {
            console.log('❌ ISSUE: phpMyAdmin still has problems');
            if (!hasPhpMyAdminContent) console.log('   - Missing phpMyAdmin content');
            if (!hasLoginForm && !hasServerSelection) console.log('   - Missing login form or server selection');
            if (cssLinks.length === 0) console.log('   - CSS files not loading');
        }
        
        // Check network requests
        const failedRequests = [];
        page.on('response', response => {
            if (response.status() >= 400) {
                failedRequests.push(`${response.status()} - ${response.url()}`);
            }
        });
        
        // Reload to check for failed requests
        await page.reload({ waitUntil: 'networkidle' });
        
        if (failedRequests.length > 0) {
            console.log('⚠️  Failed requests detected:');
            failedRequests.forEach(req => console.log(`   ${req}`));
        } else {
            console.log('✅ All requests successful');
        }
        
    } catch (error) {
        console.error('❌ Error testing phpMyAdmin:', error.message);
    } finally {
        await browser.close();
    }
}

testPhpMyAdminFix();
