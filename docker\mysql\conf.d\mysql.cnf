[mysqld]
# Basic settings
default-authentication-plugin=mysql_native_password
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# Performance settings
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT

# Connection settings
max_connections=200
max_allowed_packet=64M

# Query cache
query_cache_type=1
query_cache_size=32M

# Logging
general_log=0
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# SSL settings (optional)
ssl=0

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
