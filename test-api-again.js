#!/usr/bin/env node

/**
 * Test API endpoints again - Fresh comprehensive check
 */

const { chromium } = require('playwright');

async function testApiAgain() {
    console.log('🔄 Testing API Endpoints Again - Fresh Check');
    console.log('='.repeat(60));
    
    const browser = await chromium.launch({ 
        headless: false,
        devtools: true,
        slowMo: 100 // Slow down for better observation
    });
    
    const context = await browser.newContext({
        ignoreHTTPSErrors: true
    });
    
    const page = await context.newPage();
    
    // Comprehensive tracking
    const apiCalls = [];
    const errors = [];
    const warnings = [];
    const networkIssues = [];
    
    // Enhanced console monitoring
    page.on('console', msg => {
        const timestamp = new Date().toISOString();
        const message = {
            type: msg.type(),
            text: msg.text(),
            location: msg.location(),
            timestamp
        };
        
        if (msg.type() === 'error') {
            errors.push(message);
            console.log(`❌ [${timestamp}] ERROR: ${msg.text()}`);
            
            // Check for specific API errors
            if (msg.text().includes('api') || msg.text().includes('XMLHttpRequest') || 
                msg.text().includes('fetch') || msg.text().includes('CORS')) {
                console.log(`🔴 [API ERROR] ${msg.text()}`);
            }
        } else if (msg.type() === 'warning') {
            warnings.push(message);
            if (!msg.text().includes('Vue warn') && !msg.text().includes('intlify')) {
                console.log(`⚠️ [${timestamp}] WARNING: ${msg.text()}`);
            }
        }
    });
    
    // Network monitoring
    page.on('request', request => {
        if (request.url().includes('api.51club.local')) {
            const apiCall = {
                url: request.url(),
                method: request.method(),
                timestamp: new Date().toISOString(),
                headers: request.headers()
            };
            apiCalls.push(apiCall);
            console.log(`🌐 [REQUEST] ${request.method()} ${request.url()}`);
        }
    });
    
    page.on('response', response => {
        if (response.url().includes('api.51club.local')) {
            const status = response.status();
            const url = response.url();
            
            // Update the corresponding API call
            const apiCall = apiCalls.find(call => call.url === url && !call.status);
            if (apiCall) {
                apiCall.status = status;
                apiCall.statusText = response.statusText();
                apiCall.responseTime = new Date().toISOString();
            }
            
            const icon = status >= 200 && status < 300 ? '✅' : 
                        status >= 400 && status < 500 ? '⚠️' : '❌';
            console.log(`${icon} [RESPONSE] ${status} ${response.statusText()} - ${url}`);
        }
    });
    
    page.on('requestfailed', request => {
        if (request.url().includes('api.51club.local')) {
            const failure = {
                url: request.url(),
                method: request.method(),
                error: request.failure()?.errorText,
                timestamp: new Date().toISOString()
            };
            networkIssues.push(failure);
            console.log(`🔴 [NETWORK FAIL] ${request.method()} ${request.url()} - ${request.failure()?.errorText}`);
        }
    });
    
    try {
        console.log('🌐 Step 1: Loading main page...');
        
        const response = await page.goto('http://51club.local/#/', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        console.log(`📊 Main page loaded: ${response.status()} ${response.statusText()}`);
        
        // Wait for initial API calls
        console.log('⏳ Step 2: Waiting for initial API calls...');
        await page.waitForTimeout(8000);
        
        // Check page content and functionality
        const pageAnalysis = await page.evaluate(() => {
            return {
                title: document.title,
                url: window.location.href,
                bodyLength: document.body.innerHTML.length,
                hasContent: document.body.innerHTML.length > 10000,
                hasErrors: document.body.innerHTML.includes('error') || document.body.innerHTML.includes('Error'),
                scriptsLoaded: document.querySelectorAll('script').length,
                apiCallsVisible: document.body.innerHTML.includes('api') || document.body.innerHTML.includes('API')
            };
        });
        
        console.log('\n📊 Page Analysis:');
        console.log(`   Title: "${pageAnalysis.title}"`);
        console.log(`   Content loaded: ${pageAnalysis.hasContent ? 'Yes' : 'No'} (${pageAnalysis.bodyLength} chars)`);
        console.log(`   Scripts loaded: ${pageAnalysis.scriptsLoaded}`);
        console.log(`   Has errors in content: ${pageAnalysis.hasErrors ? 'Yes' : 'No'}`);
        
        // Step 3: Try to trigger more API calls
        console.log('\n🖱️ Step 3: Triggering additional API calls...');
        
        try {
            // Try different interactions
            const interactions = [
                { selector: 'button', action: 'click', description: 'buttons' },
                { selector: '.btn', action: 'click', description: 'btn elements' },
                { selector: 'a[href*="#"]', action: 'click', description: 'navigation links' },
                { selector: '.nav-item', action: 'click', description: 'nav items' },
                { selector: '.menu-item', action: 'click', description: 'menu items' }
            ];
            
            for (const interaction of interactions) {
                const elements = await page.$$(interaction.selector);
                if (elements.length > 0) {
                    console.log(`   Found ${elements.length} ${interaction.description}`);
                    try {
                        await elements[0].click();
                        await page.waitForTimeout(3000);
                        console.log(`   ✅ Clicked first ${interaction.description}`);
                    } catch (e) {
                        console.log(`   ⚠️ Could not click ${interaction.description}`);
                    }
                }
            }
        } catch (e) {
            console.log('   Could not perform interactions');
        }
        
        // Step 4: Final monitoring
        console.log('\n⏳ Step 4: Final monitoring period...');
        await page.waitForTimeout(5000);
        
        // Take screenshot
        await page.screenshot({ 
            path: '/mnt/g/clients/casino/51club_space/51club_main/api-test-again.png',
            fullPage: true 
        });
        console.log('📸 Screenshot saved: api-test-again.png');
        
        // Comprehensive analysis
        console.log('\n' + '='.repeat(60));
        console.log('📊 COMPREHENSIVE API TEST RESULTS');
        console.log('='.repeat(60));
        
        const successfulCalls = apiCalls.filter(call => call.status >= 200 && call.status < 300);
        const failedCalls = apiCalls.filter(call => call.status >= 400);
        const pendingCalls = apiCalls.filter(call => !call.status);
        
        console.log(`📈 API Call Summary:`);
        console.log(`   Total API requests: ${apiCalls.length}`);
        console.log(`   Successful (2xx): ${successfulCalls.length}`);
        console.log(`   Failed (4xx/5xx): ${failedCalls.length}`);
        console.log(`   Network failures: ${networkIssues.length}`);
        console.log(`   Pending/Timeout: ${pendingCalls.length}`);
        console.log(`   Console errors: ${errors.length}`);
        console.log(`   Console warnings: ${warnings.length}`);
        
        if (successfulCalls.length > 0) {
            console.log('\n✅ SUCCESSFUL API ENDPOINTS:');
            successfulCalls.forEach((call, index) => {
                const endpoint = call.url.split('/').pop();
                console.log(`   ${index + 1}. ${endpoint} - ${call.status} ${call.statusText}`);
            });
        }
        
        if (failedCalls.length > 0) {
            console.log('\n❌ FAILED API ENDPOINTS:');
            failedCalls.forEach((call, index) => {
                const endpoint = call.url.split('/').pop();
                console.log(`   ${index + 1}. ${endpoint} - ${call.status} ${call.statusText}`);
                console.log(`       URL: ${call.url}`);
            });
        }
        
        if (networkIssues.length > 0) {
            console.log('\n🌐 NETWORK ISSUES:');
            networkIssues.forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue.url} - ${issue.error}`);
            });
        }
        
        if (errors.length > 0) {
            console.log('\n❌ CONSOLE ERRORS:');
            errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error.text}`);
                if (error.location) {
                    console.log(`       Location: ${error.location.url}:${error.location.lineNumber}`);
                }
            });
        }
        
        // Overall assessment
        const totalIssues = failedCalls.length + networkIssues.length + errors.filter(e => 
            e.text.includes('api') || e.text.includes('XMLHttpRequest') || e.text.includes('fetch')
        ).length;
        
        console.log('\n🎯 OVERALL ASSESSMENT:');
        if (totalIssues === 0 && successfulCalls.length > 0) {
            console.log('🎉 EXCELLENT: All API endpoints working perfectly!');
            console.log('✅ No errors detected');
            console.log('✅ Frontend-API communication successful');
            console.log('✅ Casino platform fully operational');
        } else if (totalIssues === 0 && successfulCalls.length === 0) {
            console.log('⚠️ NO API CALLS: No API requests detected');
            console.log('   This might indicate the frontend is not making API calls');
        } else {
            console.log(`❌ ISSUES DETECTED: ${totalIssues} problems found`);
            console.log('   API endpoints need attention');
        }
        
        console.log('\n📋 RECOMMENDATIONS:');
        if (totalIssues === 0) {
            console.log('✅ No action needed - system working correctly');
        } else {
            console.log('1. Check the specific failed endpoints listed above');
            console.log('2. Verify API server is running and accessible');
            console.log('3. Check nginx proxy configuration');
            console.log('4. Review CORS settings if needed');
        }
        
    } catch (error) {
        console.error('❌ Test error:', error.message);
    } finally {
        console.log('\n🔍 Test completed. Browser will close in 5 seconds...');
        await page.waitForTimeout(5000);
        await browser.close();
    }
}

testApiAgain();
