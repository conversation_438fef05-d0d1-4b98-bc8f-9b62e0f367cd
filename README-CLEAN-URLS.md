# 🎰 51Club Casino - Clean URLs Setup Guide

## ✅ **Current Status: ALL SERVICES WORKING!**

All 51Club Casino services are now configured with clean URL access. Here's how to use them:

---

## 🌐 **Service Directory (Working Now)**

**Access**: `http://localhost:8085`

This shows a beautiful service directory with links to all services. **This is working immediately without any setup!**

---

## 🔧 **Setup Clean URLs (Optional)**

To access services with clean URLs like `http://51club.local:8085`, follow these steps:

### **Method 1: Automatic Setup (Recommended)**

1. **Right-click** on `SETUP-CLEAN-URLS.bat`
2. **Select** "Run as administrator"
3. **Click** "Yes" when prompted
4. **Follow** the on-screen instructions

### **Method 2: Manual Setup**

1. **Open Notepad as Administrator**
   - Right-click Notepad → "Run as administrator"

2. **Open hosts file**
   - File → Open → `C:\Windows\System32\drivers\etc\hosts`

3. **Add these lines at the end:**
   ```
   # 51Club Casino Local Development
   127.0.0.1    51club.local
   127.0.0.1    www.51club.local
   127.0.0.1    api.51club.local
   127.0.0.1    games.51club.local
   127.0.0.1    admin.51club.local
   127.0.0.1    db.51club.local
   ```

4. **Save the file**

5. **Flush DNS cache**
   - Open Command Prompt as Administrator
   - Run: `ipconfig /flushdns`

---

## 🎯 **Service URLs**

### **Service Directory**
- **URL**: `http://localhost:8085`
- **Status**: ✅ **Working Now**
- **Description**: Beautiful landing page with links to all services

### **Main Casino Website**
- **URL**: `http://51club.local:8085` (after hosts setup)
- **Status**: ✅ **Working**
- **Description**: Full casino website with games and user interface
- **Note**: Redirects to HTTPS (expected behavior)

### **API Service**
- **URL**: `http://api.51club.local:8085` (after hosts setup)
- **Status**: ✅ **Working**
- **Description**: Backend API for casino operations
- **Note**: Redirects to HTTPS (expected behavior)

### **Games API**
- **URL**: `http://games.51club.local:8085` (after hosts setup)
- **Status**: ✅ **Working**
- **Description**: Node.js API for game providers (Jili, JDB, Jet)
- **Endpoints**: `/getUserBalance`, `/bet`, `/sessionBet`, etc.

### **Admin Panel**
- **URL**: `http://admin.51club.local:8085` (after hosts setup)
- **Status**: ✅ **Working**
- **Description**: Administrative interface for casino management
- **Note**: Redirects to HTTPS (expected behavior)

### **Database Admin (phpMyAdmin)**
- **URL**: `http://db.51club.local:8085` (after hosts setup)
- **Status**: ✅ **Working**
- **Description**: phpMyAdmin interface for database management

---

## 🧪 **Testing Services**

### **Quick Test (No Setup Required)**
Open `test-services.html` in your browser to test all services.

### **Command Line Testing**
```bash
# Test main website
curl -H "Host: 51club.local" http://localhost:8085

# Test Games API
curl -H "Host: games.51club.local" http://localhost:8085/getUserBalance?userId=test

# Test phpMyAdmin
curl -H "Host: db.51club.local" http://localhost:8085
```

---

## 🔧 **Service Management**

### **Start All Services**
```bash
docker-compose up -d
```

### **Stop All Services**
```bash
docker-compose down
```

### **View Service Status**
```bash
docker-compose ps
```

### **View Service Logs**
```bash
docker-compose logs [service_name]
```

---

## 📊 **Service Status Summary**

| Service | URL | Status | Notes |
|---------|-----|--------|-------|
| **Service Directory** | `localhost:8085` | ✅ Working | No setup required |
| **Main Website** | `51club.local:8085` | ✅ Working | Requires hosts file |
| **API Service** | `api.51club.local:8085` | ✅ Working | Requires hosts file |
| **Games API** | `games.51club.local:8085` | ✅ Working | Requires hosts file |
| **Admin Panel** | `admin.51club.local:8085` | ✅ Working | Requires hosts file |
| **Database Admin** | `db.51club.local:8085` | ✅ Working | Requires hosts file |

---

## 🎉 **Success!**

**All services are now accessible with clean URLs!**

- ✅ Service directory working immediately
- ✅ All services responding correctly
- ✅ Domain-based routing configured
- ✅ Security headers implemented
- ✅ Rate limiting configured
- ✅ SSL redirects working (for production security)

**The 51Club Casino platform is fully operational with clean, production-like URLs!**
