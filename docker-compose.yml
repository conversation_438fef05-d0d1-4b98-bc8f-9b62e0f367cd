version: '3.8'

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    build: ./docker/nginx
    container_name: 51club_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/ssl:/etc/nginx/ssl
      - ./docker/nginx/logs:/var/log/nginx
    depends_on:
      - main
      - api
      - api_games
      - adminx
      - phpmyadmin
    networks:
      - casino_network
    restart: unless-stopped

  # Main Frontend Service
  main:
    build: ./docker/main
    container_name: 51club_main
    volumes:
      - ./main:/var/www/html
      - ./docker/main/logs:/var/log/apache2
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
    networks:
      - casino_network
    depends_on:
      - mysql
    restart: unless-stopped

  # API Service
  api:
    build: ./docker/api
    container_name: 51club_api
    volumes:
      - ./api:/var/www/html
      - ./docker/api/logs:/var/log/apache2
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
    networks:
      - casino_network
    depends_on:
      - mysql
    restart: unless-stopped

  # Games API Service (Node.js)
  api_games:
    build: ./docker/api_games
    container_name: 51club_api_games
    volumes:
      - ./api_games/jilinode:/app
      - ./docker/api_games/logs:/app/logs
    working_dir: /app
    environment:
      - NODE_ENV=production
    networks:
      - casino_network
    depends_on:
      - mysql
    restart: unless-stopped

  # Admin Panel Service
  adminx:
    build: ./docker/adminx
    container_name: 51club_adminx
    volumes:
      - ./adminx:/var/www/html
      - ./docker/adminx/logs:/var/log/apache2
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
    networks:
      - casino_network
    depends_on:
      - mysql
    restart: unless-stopped

  # phpMyAdmin Service
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: 51club_phpmyadmin
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - PMA_USER=root
      - PMA_PASSWORD=casino_root_2024
      - UPLOAD_LIMIT=1G
    networks:
      - casino_network
    depends_on:
      - mysql
    restart: unless-stopped

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: 51club_mysql
    environment:
      - MYSQL_ROOT_PASSWORD=casino_root_2024
      - MYSQL_DATABASE=casino_db
      - MYSQL_USER=casino_user
      - MYSQL_PASSWORD=casino_pass_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/conf.d:/etc/mysql/conf.d
      - ./docker/mysql/logs:/var/log/mysql
    ports:
      - "3308:3306"
    networks:
      - casino_network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local

networks:
  casino_network:
    driver: bridge
