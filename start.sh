#!/bin/bash

echo "🎰 Starting 51Club Casino Docker Environment with SSL..."
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed or not in PATH."
    exit 1
fi

echo "🔧 Building Docker images..."
docker-compose build --no-cache

echo ""
echo "🚀 Starting services..."
docker-compose up -d

echo ""
echo "⏳ Waiting for services to start..."
sleep 15

echo ""
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🔐 SSL Certificate Status:"
if docker exec 51club_nginx ls /etc/nginx/ssl/ | grep -q "51club.local.crt"; then
    echo "✅ SSL certificates generated successfully"
else
    echo "⚠️  SSL certificates not found, regenerating..."
    docker exec 51club_nginx /usr/local/bin/generate-ssl.sh
fi

echo ""
echo "🌐 Your services are now available at:"
echo "- 🏠 Main Site: https://51club.local"
echo "- 🔌 API: https://api.51club.local"
echo "- 🎮 Games API: https://games.51club.local"
echo "- ⚙️  Admin Panel: https://admin.51club.local"
echo "- 🗄️  phpMyAdmin: https://db.51club.local"
echo ""
echo "🔐 SSL Features:"
echo "- ✅ All endpoints secured with SSL/TLS"
echo "- ✅ HTTP to HTTPS redirects enabled"
echo "- ✅ Security headers configured"
echo "- ✅ Rate limiting enabled"
echo ""
echo "💡 To install SSL certificates in your browser:"
echo "   Windows: Run 'install-ssl-windows.bat' as Administrator"
echo "   Linux/WSL: Run 'sudo ./install-ssl-linux.sh'"
echo ""
echo "📝 Useful commands:"
echo "   View logs: docker-compose logs -f"
echo "   Stop services: docker-compose down"
echo "   Restart service: docker-compose restart [service_name]"
echo ""
echo "✅ Setup complete! Happy gaming! 🎲"
