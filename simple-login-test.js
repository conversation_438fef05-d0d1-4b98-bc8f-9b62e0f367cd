#!/usr/bin/env node

/**
 * Simple focused login test
 */

const { chromium } = require('playwright');

async function simpleLoginTest() {
    console.log('🔐 Simple Login Test');
    console.log('Username: 3124003124 | Password: 3124003124');
    console.log('='.repeat(50));
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Track login API calls
    let loginApiCalled = false;
    let loginResponse = null;
    
    page.on('request', request => {
        if (request.method() === 'POST' && request.postData()?.includes('3124003124')) {
            console.log(`🔑 LOGIN API CALL: ${request.url()}`);
            loginApiCalled = true;
        }
    });
    
    page.on('response', response => {
        if (response.request().postData()?.includes('3124003124')) {
            console.log(`🔑 LOGIN RESPONSE: ${response.status()}`);
            loginResponse = response;
        }
    });
    
    try {
        // Load login page
        await page.goto('http://51club.local/#/login');
        await page.waitForTimeout(3000);
        
        // Fill form
        await page.fill('input[type="text"]', '3124003124');
        await page.fill('input[type="password"]', '3124003124');
        console.log('✅ Form filled');
        
        // Try multiple submit methods
        console.log('🔄 Attempting submit...');
        
        // Method 1: Click any visible button
        const buttons = await page.$$('button');
        for (let i = 0; i < buttons.length; i++) {
            const isVisible = await buttons[i].isVisible();
            if (isVisible) {
                console.log(`Trying button ${i}...`);
                await buttons[i].click();
                await page.waitForTimeout(2000);
                if (loginApiCalled) break;
            }
        }
        
        // Method 2: Press Enter
        if (!loginApiCalled) {
            console.log('Trying Enter key...');
            await page.press('input[type="password"]', 'Enter');
            await page.waitForTimeout(2000);
        }
        
        // Check results
        if (loginApiCalled) {
            console.log('✅ Login API was called');
            if (loginResponse) {
                const responseText = await loginResponse.text();
                console.log(`📄 Response: ${responseText}`);
            }
        } else {
            console.log('❌ Login API was NOT called');
        }
        
        // Check current URL
        const currentUrl = page.url();
        console.log(`📍 Current URL: ${currentUrl}`);
        
        if (!currentUrl.includes('login')) {
            console.log('✅ Redirected - Login likely successful');
        } else {
            console.log('⚠️ Still on login page');
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await page.close();
        await browser.close();
    }
}

simpleLoginTest();
