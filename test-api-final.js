#!/usr/bin/env node

/**
 * Final comprehensive test for API endpoint: /api/webapi/GetHomeSettings
 */

const { chromium } = require('playwright');

async function testApiFinal() {
    console.log('🎯 Final API Endpoint Test: /api/webapi/GetHomeSettings');
    console.log('='.repeat(60));
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext({
        ignoreHTTPSErrors: true
    });
    const page = await context.newPage();
    
    try {
        console.log('🌐 Testing API endpoint accessibility...');
        
        const testCases = [
            {
                name: 'HTTP GET Request',
                url: 'http://api.51club.local/api/webapi/GetHomeSettings',
                method: 'GET',
                expectedStatus: 405,
                expectedMessage: 'Method not allowed'
            },
            {
                name: 'HTTP POST Request (Empty)',
                url: 'http://api.51club.local/api/webapi/GetHomeSettings',
                method: 'POST',
                body: '{}',
                expectedStatus: 200,
                expectedMessage: 'Param is Invalid'
            },
            {
                name: 'HTTPS POST Request (Empty)',
                url: 'https://api.51club.local/api/webapi/GetHomeSettings',
                method: 'POST',
                body: '{}',
                expectedStatus: 200,
                expectedMessage: 'Param is Invalid'
            },
            {
                name: 'OPTIONS Request (CORS)',
                url: 'https://api.51club.local/api/webapi/GetHomeSettings',
                method: 'OPTIONS',
                expectedStatus: 200,
                expectedMessage: 'ok'
            }
        ];
        
        const results = [];
        
        for (const testCase of testCases) {
            console.log(`\n🔍 ${testCase.name}:`);
            console.log('─'.repeat(40));
            
            try {
                const result = await page.evaluate(async (test) => {
                    const options = {
                        method: test.method,
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    };
                    
                    if (test.body && test.method !== 'GET') {
                        options.body = test.body;
                    }
                    
                    try {
                        const response = await fetch(test.url, options);
                        const text = await response.text();
                        
                        return {
                            status: response.status,
                            statusText: response.statusText,
                            content: text,
                            headers: Object.fromEntries(response.headers.entries()),
                            success: true
                        };
                    } catch (error) {
                        return {
                            error: error.message,
                            success: false
                        };
                    }
                }, testCase);
                
                if (result.success) {
                    console.log(`   Status: ${result.status} ${result.statusText}`);
                    console.log(`   Content: ${result.content}`);
                    
                    // Check if result matches expectations
                    const statusMatch = result.status === testCase.expectedStatus;
                    const contentMatch = result.content.includes(testCase.expectedMessage);
                    
                    if (statusMatch && contentMatch) {
                        console.log('   ✅ Test PASSED');
                        results.push({ ...testCase, status: 'PASS', actualStatus: result.status });
                    } else {
                        console.log('   ⚠️  Test PARTIAL');
                        results.push({ ...testCase, status: 'PARTIAL', actualStatus: result.status });
                    }
                } else {
                    console.log(`   ❌ Error: ${result.error}`);
                    results.push({ ...testCase, status: 'FAIL', error: result.error });
                }
                
            } catch (error) {
                console.log(`   ❌ Test Error: ${error.message}`);
                results.push({ ...testCase, status: 'ERROR', error: error.message });
            }
            
            await page.waitForTimeout(1000);
        }
        
        // Summary
        console.log('\n' + '='.repeat(60));
        console.log('📊 API ENDPOINT TEST SUMMARY');
        console.log('='.repeat(60));
        
        const passed = results.filter(r => r.status === 'PASS').length;
        const partial = results.filter(r => r.status === 'PARTIAL').length;
        const failed = results.filter(r => r.status === 'FAIL').length;
        const errors = results.filter(r => r.status === 'ERROR').length;
        
        results.forEach(result => {
            const icon = result.status === 'PASS' ? '✅' : 
                        result.status === 'PARTIAL' ? '⚠️' : '❌';
            console.log(`${icon} ${result.name}: ${result.status}`);
        });
        
        console.log(`\n📈 Results: ${passed} passed, ${partial} partial, ${failed} failed, ${errors} errors`);
        
        // API Status Assessment
        console.log('\n🎯 API ENDPOINT STATUS:');
        if (passed >= 3) {
            console.log('🎉 EXCELLENT: API endpoint is fully functional!');
            console.log('✅ HTTP and HTTPS both working');
            console.log('✅ Proper method validation (405 for GET)');
            console.log('✅ Parameter validation working');
            console.log('✅ CORS support enabled');
        } else if (passed >= 2) {
            console.log('✅ GOOD: API endpoint is mostly working');
            console.log('⚠️  Some minor issues detected');
        } else {
            console.log('❌ ISSUES: API endpoint has problems');
        }
        
        console.log('\n🌐 API Endpoint URLs:');
        console.log('   HTTP:  http://api.51club.local/api/webapi/GetHomeSettings');
        console.log('   HTTPS: https://api.51club.local/api/webapi/GetHomeSettings');
        
        console.log('\n📝 Usage Notes:');
        console.log('   • Use POST method (GET returns 405)');
        console.log('   • Requires specific parameters (empty body returns "Param is Invalid")');
        console.log('   • CORS enabled for cross-origin requests');
        console.log('   • Both HTTP and HTTPS supported');
        console.log('   • Referrer Policy: strict-origin-when-cross-origin');
        
    } catch (error) {
        console.error('❌ Test suite error:', error.message);
    } finally {
        await browser.close();
    }
}

testApiFinal();
